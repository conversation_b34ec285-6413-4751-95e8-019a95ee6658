from langchain_community.utilities import SQLDatabase
import pandas as pd
db = SQLDatabase.from_uri("sqlite:///databases/candidates_details_500.db")

print(f"Dialect: {db.dialect}")
print(f"Usable tables: {db.get_usable_table_names()}")

query_result = db.run("SELECT * FROM candidates ;")
print("Query result from candidates table: \n", query_result)

# print result in a df
df = pd.DataFrame(query_result)

