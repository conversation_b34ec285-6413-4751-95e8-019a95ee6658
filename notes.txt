you need to act as an expert in understanding the feature that i want to build, As an expert in building the solution using generative AI with python expert, you need to come up with the solution that I want and understand the feature and think about the solution.

This usecase is for the employeer who wants to search for this type of queries.
I want to build the search feature for my usecase, the queries are like this,

1. Find me the **nurses** with **5 years** experience in ICU with **German b2** proficiency
2. Find me **CDL classes A truck driver** with **2+ years OTR** experience
3. Find me **hazmat truck driver** with **tanker endorsement**
4. Find me **registered nurse** (RN) or licensed **practical nurse** with **3 years** experience
5. Find me **school bus driver** with **passenger endorsement** having **5+ years** of experience

Currently I dont have any type of data stored anywhere and I am planning to create a dataset.

In return When I search for the above mentioned query it should extract the entities from the query.

First understand the usecase on your own and repond me what did you understood and explain me in the bullet points than i will elaborate further.

--------------------------------------------

I want to use OpenAI for this entity extraction, also I am thinking of to use SQL as an DB because lets say if I have lakhs of record it will be processed faster comparitively with JSON and CSV

your suggestions ?

--------------------------------------------

I want to use langchain/langgraph that should work well and with the perfect prompt

your suggestions?

--------------------------------------------

I want the final output  in JSON only see 

Output Format (JSON Only): [ { "name": "Candidate Name", "profession": "Nurse", "experience_years": 6, "skills": ["ICU"], "education": "BSc Nursing", "languages": [ { "language": "German", "proficiency": "B2" } ], "match_score": 82.4 }, ... ]

--------------------------------------------

I want to have that matching score that should be the dynamic and coming through the LLM logic onlyjust to avoid static calls and logic

--------------------------------------------

What should be the method to store the conversation history?

--------------------------------------------
Prompt with instructions

You are a candidate matching engine
You will receive:
    - A natural language search query entered by an employer
    - A list of candidates with structured information (profession, specialization, experience, education, languages, etc.)

Your task is to return only the candidates who match the query exactly or closely, based on clear logical conditions. Additionally, compute a weighted match score for each candidate based on adaptive weight distribution.

    - Employer's Search Query: "{user_query}"
    - Candidate List: {candidate_list}

Your Instructions:
    1. Interpret the search query and extract filters:
        - Profession (e.g., Nurse)
        - Specialization(s) or Skills (e.g., ICU)
        - Minimum years of experience (e.g., > 5 years)
        - Required education level (if specified)
        - Required language(s) and proficiency level (e.g., German B2)
    
    2. For each candidate, check the following:
        - Required profession (or closely related title)
        - Required specialization/skills (e.g., ICU)
        - Required years of experience
        - Required education level (if specified)
        - Required language and minimum proficiency level
        - If language proficiency is slightly higher (e.g., C1 instead of B2), accept it

For candidates missing one or more of the four parameters (skills, experience, education, language), redistribute the weights proportionally among the remaining parameters:
    - Default weights:
        - Skills: 40%
        - Experience: 20%
        - Education: 20%
        - Language: 20%
    - Example redistribution:
        - If experience is missing: Skills 46.67%, Education 26.67%, Language 26.67%
        - If skills is missing: Experience 33.33%, Education 33.33%, Language 33.33%

Calculate Match Score for each candidate using:
    - Match Score = (match_percentage_for_each_parameter * weight_of_that_parameter)
    - Sum all weighted scores to get the final score (e.g., Skill match = 80% * 0.4 = 32)

Return a list of top 25 candidates sorted by Match Score in descending order.
Output only those candidates whose match score is at least 10 (i.e., 10%).
Return the result as a JSON array of matching candidates with their computed match score. Do not explain or add extra text.

Edge Cases to Handle:
    - Ignore candidates with entirely missing data across all parameters
    - Accept equivalent or higher language levels (e.g., B2+ means B2, C1, C2 are valid)
    - Use strict match for specializations
    - If query says "more than 5 years", only include candidates with > 5 years
    - If query says "at least 5 years", include candidates with 5 or more
    - Handle queries that contain multiple specializations or language combinations

Output Format (JSON Only):
[{
    "name": "Candidate Name", 
    "profession": "Nurse", 
    "experience_years": 6, 
    "skills": ["ICU"], 
    "education": "BSc Nursing", 
    "languages": [{
        "language": "German", 
        "proficiency": "B2"}], 
    "match_score": 82.4
}]

--------------------------------


Now that I have created a database of the 50 records, I want to use LLM OpenAI gpt-4o model to extract the key tags from the user query

From the query it will extract the key NER tags such as Skills, Experience, Language, Education

Note: It is not necessary that everytime this will be entered by the user, it depends on the query what it looks like.

Also to extract the following fields, I want to use openai function calling that should give me structured output using pydantic, 

Show me the codebase for the same once I start getting the fields I will tell you to write the logic of the matching scoring, that should be happening through the LLM only, 

Note: No static code will be there anywhere