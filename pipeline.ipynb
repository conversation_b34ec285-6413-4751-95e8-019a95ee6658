{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5c3f8ed5", "metadata": {}, "outputs": [], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from openai import OpenAI\n", "from pydantic import BaseModel, ValidationError"]}, {"cell_type": "code", "execution_count": 2, "id": "f051a732", "metadata": {}, "outputs": [], "source": ["class LanguageItem(BaseModel):\n", "    language: str\n", "    proficiency: Optional[str] = None\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str]\n", "    skills: Optional[List[str]]\n", "    experience_years: Optional[int]\n", "    education: Optional[str]\n", "    languages: Optional[List[LanguageItem]]"]}, {"cell_type": "code", "execution_count": 3, "id": "76ee13c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["key is: ********************************************************************************************************************************************************************\n"]}], "source": ["# Set your OpenAI API key\n", "api_key = os.getenv(\"OPENAI_API_KEY\")  # or hardcode it for testing\n", "print(f\"key is: {api_key}\")\n", "\n", "client = OpenAI(api_key=api_key)"]}, {"cell_type": "code", "execution_count": 4, "id": "5668ea2d", "metadata": {}, "outputs": [], "source": ["def extract_query_fields(user_query: str) -> ParsedQuery:\n", "    function_schema = {\n", "        \"name\": \"extract_query\",\n", "        \"description\": \"Extract key structured fields from employer's natural language query\",\n", "        \"parameters\": ParsedQuery.model_json_schema()\n", "    }\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"gpt-4o\",\n", "        temperature=0,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are a recruiter assistant. Extract structured fields from the user's hiring query.\"\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": user_query\n", "            }\n", "        ],\n", "        functions=[function_schema],\n", "        function_call={\"name\": \"extract_query\"}\n", "    )\n", "\n", "    # Extract function arguments\n", "    args = response.choices[0].message.function_call.arguments  \n", "    #response[\"choices\"][0][\"message\"][\"function_call\"][\"arguments\"]\n", "\n", "    import json\n", "    parsed_args = json.loads(args)\n", "\n", "    try:\n", "        return ParsedQuery(**parsed_args)\n", "    except ValidationError as e:\n", "        print(\"❌ Validation failed:\", e)\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ee12a338", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"nurse\",\n", "  \"skills\": [\n", "    \"ICU\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": [\n", "    {\n", "      \"language\": \"German\",\n", "      \"proficiency\": \"B2\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["if __name__ == \"__main__\":\n", "    query = \"Find me nurses with 5 years experience in ICU with German B2 proficiency\"\n", "    result = extract_query_fields(query)\n", "    print(result.model_dump_json(indent=2))\n"]}, {"cell_type": "markdown", "id": "d0255424", "metadata": {}, "source": ["### using langchain structured output parser"]}, {"cell_type": "code", "execution_count": null, "id": "e72e760d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "58ae3298", "metadata": {}, "outputs": [], "source": ["# import os\n", "# from typing import List, Optional\n", "\n", "# from langchain_openai import ChatOpenAI\n", "# from langchain.output_parsers import StructuredOutputParser\n", "# from langchain.prompts import ChatPromptTemplate\n", "# from pydantic import BaseModel, Field\n", "\n", "\n", "# class LanguageItem(BaseModel):\n", "#     language: str = Field(..., description=\"The language spoken by the candidate\")\n", "#     proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "# class ParsedQuery(BaseModel):\n", "#     profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "#     skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "#     experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "#     education: Optional[str] = Field(None, description=\"Education requirements\")\n", "#     languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "# api_key = os.getenv(\"OPENAI_API_KEY\")  # or hardcode it for testing\n", "# print(f\"key is: {api_key}\")\n", "\n", "# # Initialize OpenAI model (GPT-4o)\n", "# llm = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "# model_with_structured_output = llm.with_structured_output(ParsedQuery)\n", "# # Use StructuredOutputParser with your model\n", "# parser = StructuredOutputParser.model_validate(ParsedQuery)\n", "\n", "# format_instructions = parser.get_format_instructions()\n", "\n", "# prompt = ChatPromptTemplate.from_messages([\n", "#     (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "#     (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "# ])\n", "\n", "# def extract_structured_query(query: str) -> ParsedQuery:\n", "#     formatted_prompt = prompt.format_messages(\n", "#         query=query,\n", "#         format_instructions=format_instructions\n", "#     )\n", "\n", "#     response = llm(formatted_prompt)\n", "#     parsed = parser.parse(response.content)\n", "#     return parsed\n", "\n", "# # 🧪 Test\n", "# if __name__ == \"__main__\":\n", "#     query = \"Find me a school bus driver with passenger endorsement having 5+ years of experience\"\n", "#     result = extract_structured_query(query)\n", "#     print(result)\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "049a9cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "markdown", "id": "32688616", "metadata": {}, "source": ["profession='nurse' skills=['ICU'] experience_years=5 education=None languages=[LanguageItem(language='German', proficiency='B2')]\n", "\n", "{\n", "  \"profession\": \"CDL Class A Truck Driver\",\n", "  \"skills\": [\n", "    \"OTR\"\n", "  ],\n", "  \"experience_years\": 2,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n", "\n", "{\n", "  \"profession\": \"hazmat truck driver\",\n", "  \"skills\": [\n", "    \"tanker endorsement\"\n", "  ],\n", "  \"experience_years\": null,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n", "\n", "\n", "{\n", "  \"profession\": \"Registered Nurse (RN) or Licensed Practical Nurse\",\n", "  \"skills\": null,\n", "  \"experience_years\": 3,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n", "\n", "\n", "{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}"]}, {"cell_type": "code", "execution_count": 25, "id": "01b225b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsed content: profession='nurse' skills=None experience_years='5' education=None languages=None\n", "\n", "\n", "{\n", "  \"profession\": \"nurse\",\n", "  \"skills\": null,\n", "  \"experience_years\": \"5\",\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(\n", "        None, description=\"Proficiency level in the language (e.g., B2, C1)\"\n", "    )\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(\n", "        None, description=\"The profession or job title mentioned\"\n", "    )\n", "    skills: Optional[List[str]] = Field(\n", "        None, description=\"List of skills required for the job\"\n", "    )\n", "    experience_years: Optional[str] = Field(\n", "        None, description=\"Years of experience required, e.g., '5+', 'more than 3', '5 plus', 'at least 2 years', 'less than 2 years\"\n", "    )\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(\n", "        None, description=\"List of languages and their proficiency levels\"\n", "    )\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\\n\\n\"\n", "            \"Your job is to carefully understand and analyze these queries and extract the following structured information:\\n\"\n", "            \"- profession: the job title or role the employer is seeking\\n\"\n", "            \"- skills: a list of specific skills or qualifications mentioned\\n\"\n", "            \"- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\\n\"\n", "            \"- education: any educational requirements, such as 'bachelor’s degree', 'MBA', etc.\\n\"\n", "            \"- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\\n\\n\"\n", "            \"You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\\n\"\n", "            \"Be strict and precise in your interpretation.\\n\"\n", "            \"Output your response in the exact format described in the instructions below.\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "def extract_structured_query(query: str) -> Optional[ParsedQuery]:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query, format_instructions=format_instructions\n", "    )\n", "    \n", "    # print(f\"Formatted prompt: {formatted_prompt}\\n\\n\")\n", "\n", "    response = llm.invoke(formatted_prompt)\n", "    # print(f\"Response content: {response}\\n\\n\")\n", "    parsed = parser.parse(response.content)\n", "    print(f\"Parsed content: {parsed}\\n\\n\")\n", "\n", "    # Check if all fields are None or empty\n", "    if not any(\n", "        [\n", "            parsed.profession,\n", "            parsed.skills,\n", "            parsed.experience_years,\n", "            parsed.education,\n", "            parsed.languages,\n", "        ]\n", "    ):\n", "        return None\n", "\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"can you pls help me with the nurses having 5 years of experience\"\n", "    result = extract_structured_query(query)\n", "\n", "    if result is None:\n", "        print(\n", "            \"Hey, I am a Matching SQL Assistant: Please search for a job title, skills, or relevant experience so I can assist you better!\"\n", "        )\n", "    else:\n", "        print(result.model_dump_json(indent=2))"]}, {"cell_type": "code", "execution_count": 26, "id": "c8ab4731", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Parsed structured output:\n", "{\n", "  \"profession\": \"truck driver\",\n", "  \"skills\": null,\n", "  \"experience_years\": \"more than 12 years\",\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n", "\n", "🔍 Running SQL:\n", "SELECT * FROM candidates WHERE LOWER(profession) LIKE '%truck driver%' AND experience_years >= 12\n", "\n", "✅ Found candidates:\n", "{'id': 211, 'name': '<PERSON>', 'age': 35, 'country': 'Romania', 'profession': 'Truck Driver', 'skills': None, 'education': 'Certificate Program', 'experience_years': 17, 'language': 'Chinese Intermediate'}\n", "{'id': 256, 'name': '<PERSON>', 'age': 54, 'country': 'Botswana', 'profession': 'Truck Driver', 'skills': 'OTR, Defensive Driving, Hazmat, Tanker', 'education': 'Certificate Program', 'experience_years': 20, 'language': 'English Basic'}\n", "{'id': 270, 'name': '<PERSON>', 'age': 56, 'country': 'Hungary', 'profession': 'Truck Driver', 'skills': None, 'education': 'Certificate Program', 'experience_years': 16, 'language': None}\n", "{'id': 275, 'name': 'Mr. <PERSON>', 'age': 26, 'country': 'Suriname', 'profession': 'Truck Driver', 'skills': 'Defensive Driving, Tanker, <PERSON><PERSON><PERSON>, OTR', 'education': 'Associate Degree', 'experience_years': 17, 'language': None}\n", "{'id': 330, 'name': '<PERSON>', 'age': 35, 'country': 'Germany', 'profession': 'Truck Driver', 'skills': 'Defensive Driving, Hazmat, Tanker', 'education': 'Certificate Program', 'experience_years': 17, 'language': 'English Intermediate'}\n", "{'id': 367, 'name': '<PERSON>', 'age': 28, 'country': 'Seychelles', 'profession': 'Truck Driver', 'skills': '<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Defensive Driving', 'education': 'M Tech', 'experience_years': 13, 'language': 'Spanish Basic'}\n", "{'id': 429, 'name': '<PERSON>', 'age': 57, 'country': 'Saint Kitts and Nevis', 'profession': 'Truck Driver', 'skills': '<PERSON>er, O<PERSON>', 'education': 'Diploma', 'experience_years': 14, 'language': 'German C1'}\n", "{'id': 462, 'name': '<PERSON>', 'age': 50, 'country': 'Eritrea', 'profession': 'Truck Driver', 'skills': 'Defensive Driving, OTR, Hazmat', 'education': 'Diploma', 'experience_years': 20, 'language': 'Spanish Fluent'}\n", "{'id': 474, 'name': '<PERSON><PERSON>', 'age': 45, 'country': 'Botswana', 'profession': 'Truck Driver', 'skills': 'Tanker, Defensive Driving, Hazmat', 'education': 'Certificate Program', 'experience_years': 18, 'language': 'French Fluent'}\n", "{'id': 489, 'name': '<PERSON>', 'age': 55, 'country': 'Jamaica', 'profession': 'Truck Driver', 'skills': 'Tanker, Hazmat', 'education': 'B Tech', 'experience_years': 17, 'language': 'French Fluent'}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "import sqlite3\n", "\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# 🎯 Define output model\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(\n", "        None, description=\"Proficiency level (e.g., B2, C1)\"\n", "    )\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"Profession or job title\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of required skills\")\n", "    experience_years: Optional[str] = Field(\n", "        None, description=\"Experience (e.g., '5+', 'less than 2')\"\n", "    )\n", "    education: Optional[str] = Field(None, description=\"Education level\")\n", "    languages: Optional[List[LanguageItem]] = Field(\n", "        None, description=\"Languages and proficiency\"\n", "    )\n", "\n", "\n", "# 🧠 Initialize LLM\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0)\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "\n", "# 📝 Prompt\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\\n\\n\"\n", "            \"Your job is to carefully understand and analyze these queries and extract the following structured information:\\n\"\n", "            \"- profession: the job title or role the employer is seeking\\n\"\n", "            \"- skills: a list of specific skills or qualifications mentioned\\n\"\n", "            \"- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number or phrase as-is.\\n\"\n", "            \"- education: any educational requirements, such as 'bachelor’s degree', 'MBA', etc.\\n\"\n", "            \"- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\\n\\n\"\n", "            \"You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\\n\"\n", "            \"Be strict and precise in your interpretation.\\n\"\n", "            \"Output your response in the exact format described in the instructions below.\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "def extract_structured_query(query: str) -> Optional[ParsedQuery]:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query, format_instructions=format_instructions\n", "    )\n", "    response = llm.invoke(formatted_prompt)\n", "    parsed = parser.parse(response.content)\n", "\n", "    # Check if all fields are empty\n", "    if not any(\n", "        [\n", "            parsed.profession,\n", "            parsed.skills,\n", "            parsed.experience_years,\n", "            parsed.education,\n", "            parsed.languages,\n", "        ]\n", "    ):\n", "        return None\n", "\n", "    return parsed\n", "\n", "\n", "def generate_sql(parsed: ParsedQuery) -> str:\n", "    where_clauses = []\n", "\n", "    if parsed.profession:\n", "        where_clauses.append(f\"LOWER(profession) LIKE '%{parsed.profession.lower()}%'\")\n", "\n", "    if parsed.skills:\n", "        for skill in parsed.skills:\n", "            where_clauses.append(f\"LOWER(skills) LIKE '%{skill.lower()}%'\")\n", "\n", "    if parsed.experience_years:\n", "        if any(keyword in parsed.experience_years for keyword in [\"less\", \"<\"]):\n", "            try:\n", "                years = int(\"\".join(filter(str.isdigit, parsed.experience_years)))\n", "                where_clauses.append(f\"experience_years < {years}\")\n", "            except:\n", "                pass\n", "        elif any(\n", "            keyword in parsed.experience_years\n", "            for keyword in [\"more\", \"+\", \"at least\", \">\"]\n", "        ):\n", "            try:\n", "                years = int(\"\".join(filter(str.isdigit, parsed.experience_years)))\n", "                where_clauses.append(f\"experience_years >= {years}\")\n", "            except:\n", "                pass\n", "        else:\n", "            try:\n", "                years = int(\"\".join(filter(str.isdigit, parsed.experience_years)))\n", "                where_clauses.append(f\"experience_years = {years}\")\n", "            except:\n", "                pass\n", "\n", "    if parsed.education:\n", "        where_clauses.append(f\"LOWER(education) LIKE '%{parsed.education.lower()}%'\")\n", "\n", "    if parsed.languages:\n", "        for lang in parsed.languages:\n", "            where_clauses.append(f\"LOWER(languages) LIKE '%{lang.language.lower()}%'\")\n", "\n", "    sql_query = \"SELECT * FROM candidates\"\n", "    if where_clauses:\n", "        sql_query += \" WHERE \" + \" AND \".join(where_clauses)\n", "\n", "    return sql_query\n", "\n", "\n", "def run_sql_query(sql: str):\n", "    conn = sqlite3.connect(\"databases/candidates_details_500.db\")\n", "    cursor = conn.cursor()\n", "\n", "    try:\n", "        print(f\"\\n🔍 Running SQL:\\n{sql}\\n\")\n", "        cursor.execute(sql)\n", "        rows = cursor.fetchall()\n", "        col_names = [desc[0] for desc in cursor.description]\n", "        results = [dict(zip(col_names, row)) for row in rows]\n", "\n", "        if not results:\n", "            print(\"🚫 No candidates found matching your criteria.\")\n", "        else:\n", "            print(\"✅ Found candidates:\")\n", "            for res in results:\n", "                print(res)\n", "\n", "    except sqlite3.Error as e:\n", "        print(\"❌ SQLite error:\", e)\n", "\n", "    finally:\n", "        conn.close()\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"can you pls help me with the truck driver from having more than 12 years of experience\"\n", "    result = extract_structured_query(query)\n", "\n", "    if result is None:\n", "        print(\n", "            \"Hey, I am a Matching SQL Assistant: Please search for a job title, skills, or relevant experience so I can assist you better!\"\n", "        )\n", "    else:\n", "        print(\"✅ Parsed structured output:\")\n", "        print(result.model_dump_json(indent=2))\n", "\n", "        sql = generate_sql(result)\n", "        run_sql_query(sql)"]}, {"cell_type": "markdown", "id": "6e554f05", "metadata": {}, "source": ["### LLM"]}, {"cell_type": "code", "execution_count": 1, "id": "4063cfa1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the structured prompt is: input_variables=['format_instructions', 'query'] input_types={} partial_variables={} messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\\n\\nYour job is to carefully understand and analyze these queries and extract the following structured information:\\n- profession\\n- skills\\n- experience_years\\n- education\\n- languages\\n\\nIf a field is not mentioned, leave it null. Be strict, focused, and precise.'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['format_instructions', 'query'], input_types={}, partial_variables={}, template='{query}\\n\\n{format_instructions}'), additional_kwargs={})]\n", "\n", "\n"]}], "source": ["import os\n", "import sqlite3\n", "import json\n", "from typing import List, Optional\n", "\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# 🧩 Pydantic Models\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(\n", "        None, description=\"Proficiency level (e.g., B2, C1)\"\n", "    )\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"Profession or job title\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of required skills\")\n", "    experience_years: Optional[str] = Field(\n", "        None, description=\"Experience required (e.g. '5+', 'less than 2')\"\n", "    )\n", "    education: Optional[str] = Field(None, description=\"Education level\")\n", "    languages: Optional[List[LanguageItem]] = Field(\n", "        None, description=\"Languages and proficiency\"\n", "    )\n", "\n", "\n", "# 🔧 Init LLM\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0)\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "\n", "# 📥 Structured data extraction prompt\n", "structured_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\\n\\n\"\n", "            \"Your job is to carefully understand and analyze these queries and extract the following structured information:\\n\"\n", "            \"- profession\\n- skills\\n- experience_years\\n- education\\n- languages\\n\\n\"\n", "            \"If a field is not mentioned, leave it null. Be strict, focused, and precise.\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "print(f\"the structured prompt is: {structured_prompt}\\n\\n\")\n", "\n", "def extract_structured_query(query: str) -> Optional[ParsedQuery]:\n", "    formatted_prompt = structured_prompt.format_messages(\n", "        query=query, format_instructions=format_instructions\n", "    )\n", "    response = llm.invoke(formatted_prompt)\n", "    parsed = parser.parse(response.content)\n", "\n", "    if not any(\n", "        [\n", "            parsed.profession,\n", "            parsed.skills,\n", "            parsed.experience_years,\n", "            parsed.education,\n", "            parsed.languages,\n", "        ]\n", "    ):\n", "        return None\n", "    return parsed\n", "\n", "\n", "# 💡 SQL generation using GPT\n", "def generate_sql_with_llm(parsed: ParsedQuery) -> str:\n", "    json_data = parsed.model_dump()\n", "\n", "    prompt = ChatPromptTemplate.from_messages(\n", "        [\n", "            (\n", "                \"system\",\n", "                \"You are a SQL assistant. Given structured JSON describing candidate requirements, generate a valid SQL SELECT query \"\n", "                \"to find matching candidates from a SQLite database named `candidate_details_500.db` and a table named `candidate`.\\n\\n\"\n", "                \"The table columns include: profession, skills, experience_years, education, languages.\\n\"\n", "                \"Use case-insensitive LIKE for text fields. Handle partial matches.\\n\"\n", "                \"Do not hallucinate missing fields. Only use fields provided.\",\n", "            ),\n", "            (\n", "                \"user\",\n", "                \"JSON input:\\n{json_input}\\n\\nWrite a SQL SELECT statement using this.\",\n", "            ),\n", "        ]\n", "    )\n", "\n", "    formatted = prompt.format_messages(json_input=json.dumps(json_data, indent=2))\n", "    print(f\"the formatted prompt is: {formatted}\\n\\n\")\n", "    response = llm.invoke(formatted)\n", "    return response.content.strip()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d74512a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Extracted:\n", "{\n", "  \"profession\": \"nurse\",\n", "  \"skills\": null,\n", "  \"experience_years\": \"3+\",\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n", "the formatted prompt is: [SystemMessage(content='You are a SQL assistant. Given structured JSON describing candidate requirements, generate a valid SQL SELECT query to find matching candidates from a SQLite database named `candidate_details_500.db` and a table named `candidate`.\\n\\nThe table columns include: profession, skills, experience_years, education, languages.\\nUse case-insensitive LIKE for text fields. Handle partial matches.\\nDo not hallucinate missing fields. Only use fields provided.', additional_kwargs={}, response_metadata={}), HumanMessage(content='JSON input:\\n{\\n  \"profession\": \"nurse\",\\n  \"skills\": null,\\n  \"experience_years\": \"3+\",\\n  \"education\": null,\\n  \"languages\": null\\n}\\n\\nWrite a SQL SELECT statement using this.', additional_kwargs={}, response_metadata={})]\n", "\n", "\n", "📝 SQL Generated by GPT:\n", " ```sql\n", "SELECT *\n", "FROM candidate\n", "WHERE LOWER(profession) LIKE '%nurse%'\n", "  AND experience_years >= 3;\n", "```\n", "\n", "🔍 Running SQL:\n", "```sql\n", "SELECT *\n", "FROM candidate\n", "WHERE LOWER(profession) LIKE '%nurse%'\n", "  AND experience_years >= 3;\n", "```\n", "\n", "❌ SQLite error: near \"```sql\n", "SELECT *\n", "FROM candidate\n", "WHERE LOWER(profession) LIKE '%nurse%'\n", "  AND experience_years >= 3;\n", "```\": syntax error\n"]}], "source": ["\n", "\n", "# 🧪 Run query on SQLite\n", "def run_sql_query(sql: str):\n", "    conn = sqlite3.connect(\"databases/candidate_details_500.db\")\n", "    cursor = conn.cursor()\n", "    try:\n", "        print(f\"\\n🔍 Running SQL:\\n{sql}\\n\")\n", "        cursor.execute(sql)\n", "        rows = cursor.fetchall()\n", "        col_names = [desc[0] for desc in cursor.description]\n", "        results = [dict(zip(col_names, row)) for row in rows]\n", "\n", "        if not results:\n", "            print(\"🚫 No candidates found matching your criteria.\")\n", "        else:\n", "            print(\"✅ Found candidates:\")\n", "            for res in results:\n", "                print(res)\n", "\n", "    except sqlite3.Error as e:\n", "        print(\"❌ SQLite error:\", e)\n", "    finally:\n", "        conn.close()\n", "\n", "\n", "# 🔁 Main Flow\n", "if __name__ == \"__main__\":\n", "    query = \"Can you help me find a nurse, 3+ years?\"\n", "\n", "    result = extract_structured_query(query)\n", "\n", "    if result is None:\n", "        print(\n", "            \"Hey, I am a Matching SQL Assistant: Please enter a valid job title, skills, or experience.\"\n", "        )\n", "    else:\n", "        print(\"✅ Extracted:\")\n", "        print(result.model_dump_json(indent=2))\n", "\n", "        sql = generate_sql_with_llm(result)\n", "        print(\"📝 SQL Generated by GPT:\\n\", sql)\n", "\n", "        run_sql_query(sql)"]}, {"cell_type": "code", "execution_count": null, "id": "fb00418f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}