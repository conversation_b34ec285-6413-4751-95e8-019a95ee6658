import json
import random
import faker

from sqlalchemy import create_engine, Column, Integer, String, Text
from sqlalchemy.orm import declarative_base, sessionmaker

# -------------------------------
# 1. Setup <PERSON>aker and Database
# -------------------------------
fake = faker.Faker()
Base = declarative_base()
engine = create_engine("sqlite:///candidates.db")  # <-- file-based DB
Session = sessionmaker(bind=engine)
session = Session()

# -------------------------------
# 2. Define Candidate Model
# -------------------------------
class Candidate(Base):
    __tablename__ = "candidates"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    profession = Column(String)
    experience_years = Column(Integer)
    skills = Column(Text)
    education = Column(String)
    languages = Column(Text)
    certifications = Column(Text)
    endorsements = Column(Text)
    licenses = Column(Text)

Base.metadata.create_all(engine)

# -------------------------------
# 3. Mock Data Pools
# -------------------------------
professions = [
    ("Nurse", ["ICU", "Pediatrics", "Surgery"]),
    ("Truck Driver", ["OTR", "Long Haul", "Hazmat"]),
    ("Electrician", ["Residential", "Commercial"]),
    ("Software Engineer", ["Python", "Java", "React"]),
    ("Doctor", ["Cardiology", "Orthopedics"]),
]

educations = ["High School", "BSc Nursing", "MBBS", "BTech", "Diploma"]

languages = [
    {"language": "German", "proficiencies": ["A2", "B1", "B2", "C1"]},
    {"language": "English", "proficiencies": ["B1", "B2", "C1", "C2"]},
    {"language": "Spanish", "proficiencies": ["A2", "B1", "B2"]},
]

certification_pool = ["CDL Class A", "PALS", "BLS", "ACLS", "AWS Certified"]
endorsement_pool = ["Tanker", "Doubles", "Triples"]
license_pool = ["Hazmat", "Medical License", "Electrical License"]

# -------------------------------
# 4. Generate 20 Mock Candidates
# -------------------------------
mock_candidates = []

for _ in range(20):
    name = fake.name()
    profession, skill_options = random.choice(professions)
    skills = random.sample(skill_options, k=1)
    experience = random.randint(1, 15)
    education = random.choice(educations)

    lang_info = random.choice(languages)
    lang = {
        "language": lang_info["language"],
        "proficiency": random.choice(lang_info["proficiencies"])
    }

    certifications = random.sample(certification_pool, k=random.randint(0, 2))
    endorsements = random.sample(endorsement_pool, k=random.randint(0, 2))
    licenses = random.sample(license_pool, k=random.randint(0, 2))

    candidate = Candidate(
        name=name,
        profession=profession,
        experience_years=experience,
        skills=json.dumps(skills),
        education=education,
        languages=json.dumps([lang]),
        certifications=json.dumps(certifications),
        endorsements=json.dumps(endorsements),
        licenses=json.dumps(licenses),
    )

    mock_candidates.append(candidate)

# -------------------------------
# 5. Save to Database
# -------------------------------
session.add_all(mock_candidates)
session.commit()

print("✅ 20 mock candidates saved to 'candidates.db'")
