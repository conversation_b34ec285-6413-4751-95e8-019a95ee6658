{"cells": [{"cell_type": "code", "execution_count": 1, "id": "08495b9d", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine, Column, Integer, String, JSON, Table, MetaData, Text\n", "from sqlalchemy.orm import declarative_base, sessionmaker\n", "from pydantic import BaseModel\n", "from typing import List, Optional, Dict\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "e0bff385", "metadata": {}, "outputs": [], "source": ["# Step 1: Setup SQLite (in-memory for this demo)\n", "engine = create_engine(\"sqlite:///:memory:\", echo=False)\n", "Base = declarative_base()\n", "Session = sessionmaker(bind=engine)\n", "session = Session()"]}, {"cell_type": "code", "execution_count": 3, "id": "52790be0", "metadata": {}, "outputs": [], "source": ["# Step 2: Define SQLAlchemy candidate table\n", "class Candidate(Base):\n", "    __tablename__ = \"candidates\"\n", "\n", "    id = Column(Integer, primary_key=True)\n", "    name = Column(String)\n", "    profession = Column(String)\n", "    experience_years = Column(Integer)\n", "    skills = Column(Text)  # Store JSON as string\n", "    education = Column(String)\n", "    languages = Column(Text)  # Store JSON as string\n", "    certifications = Column(Text)\n", "    endorsements = Column(Text)\n", "    licenses = Column(Text)"]}, {"cell_type": "code", "execution_count": 4, "id": "3342e318", "metadata": {}, "outputs": [], "source": ["# Create table\n", "Base.metadata.create_all(engine)"]}, {"cell_type": "code", "execution_count": 5, "id": "cf510d7e", "metadata": {}, "outputs": [], "source": ["# Step 3: Insert mock candidate data into DB\n", "mock_candidates = [\n", "    Candidate(\n", "        name=\"<PERSON>\",\n", "        profession=\"Nurse\",\n", "        experience_years=6,\n", "        skills=json.dumps([\"ICU\"]),\n", "        education=\"BSc Nursing\",\n", "        languages=json.dumps([{\"language\": \"German\", \"proficiency\": \"B2\"}]),\n", "        certifications=json.dumps([]),\n", "        endorsements=json.dumps([]),\n", "        licenses=json.dumps([])\n", "    ),\n", "    Candidate(\n", "        name=\"<PERSON>\",\n", "        profession=\"Nurse\",\n", "        experience_years=5,\n", "        skills=json.dumps([\"ICU\", \"ER\"]),\n", "        education=\"Diploma in Nursing\",\n", "        languages=json.dumps([{\"language\": \"German\", \"proficiency\": \"B1\"}]),\n", "        certifications=json.dumps([]),\n", "        endorsements=json.dumps([]),\n", "        licenses=json.dumps([])\n", "    ),\n", "    Candidate(\n", "        name=\"<PERSON>\",\n", "        profession=\"Truck Driver\",\n", "        experience_years=3,\n", "        skills=json.dumps([\"OTR\"]),\n", "        education=\"High School\",\n", "        languages=json.dumps([{\"language\": \"English\", \"proficiency\": \"C1\"}]),\n", "        certifications=json.dumps([\"CDL Class A\"]),\n", "        endorsements=json.dumps([\"Tanker\"]),\n", "        licenses=json.dumps([\"Hazmat\"])\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": 6, "id": "114f86b9", "metadata": {}, "outputs": [], "source": ["session.add_all(mock_candidates)\n", "session.commit()"]}, {"cell_type": "code", "execution_count": null, "id": "0053248a", "metadata": {}, "outputs": [], "source": ["# Step 4: Define search query schema using Pydantic\n", "class SearchQuery(BaseModel):\n", "    role: str\n", "    specialization: Optional[str] = None\n", "    experience_years: Optional[int] = None\n", "    language: Optional[str] = None\n", "    language_proficiency: Optional[str] = None\n", "    certifications: Optional[List[str]] = []\n", "    endorsements: Optional[List[str]] = []\n", "    licenses: Optional[List[str]] = []"]}, {"cell_type": "code", "execution_count": 8, "id": "d4975777", "metadata": {}, "outputs": [], "source": ["# Simulate OpenAI output\n", "search_input = SearchQuery(\n", "    role=\"Nurse\",\n", "    specialization=\"ICU\",\n", "    experience_years=5,\n", "    language=\"German\",\n", "    language_proficiency=\"B2\"\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "0149f226", "metadata": {}, "outputs": [], "source": ["# Step 5: Scoring logic and filtering\n", "def compute_match_score(candidate: Candidate, filters: SearchQuery) -> float:\n", "    score = 0\n", "    weight = {\n", "        \"role\": 30,\n", "        \"specialization\": 20,\n", "        \"experience_years\": 20,\n", "        \"language_proficiency\": 15,\n", "        \"certifications\": 15\n", "    }\n", "\n", "    if filters.role.lower() in candidate.profession.lower():\n", "        score += weight[\"role\"]\n", "    skills = json.loads(candidate.skills)\n", "    if filters.specialization and filters.specialization.lower() in [s.lower() for s in skills]:\n", "        score += weight[\"specialization\"]\n", "    if filters.experience_years and candidate.experience_years >= filters.experience_years:\n", "        score += weight[\"experience_years\"]\n", "    langs = json.loads(candidate.languages)\n", "    for lang in langs:\n", "        if lang[\"language\"].lower() == filters.language.lower() and lang[\"proficiency\"].upper() == filters.language_proficiency.upper():\n", "            score += weight[\"language_proficiency\"]\n", "\n", "    return round(score, 1)"]}, {"cell_type": "code", "execution_count": 10, "id": "a0c87279", "metadata": {}, "outputs": [], "source": ["# Step 6: Format matching candidates to JSON\n", "def search_candidates(filters: SearchQuery) -> List[Dict]:\n", "    candidates = session.query(Candidate).all()\n", "    results = []\n", "\n", "    for c in candidates:\n", "        score = compute_match_score(c, filters)\n", "        if score > 0:  # Only return those with non-zero match\n", "            results.append({\n", "                \"name\": c.name,\n", "                \"profession\": c.profession,\n", "                \"experience_years\": c.experience_years,\n", "                \"skills\": json.loads(c.skills),\n", "                \"education\": c.education,\n", "                \"languages\": json.loads(c.languages),\n", "                \"match_score\": score\n", "            })\n", "\n", "    return sorted(results, key=lambda x: x[\"match_score\"], reverse=True)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "1a5256a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"name\": \"<PERSON>\",\n", "    \"profession\": \"Nurse\",\n", "    \"experience_years\": 6,\n", "    \"skills\": [\n", "      \"ICU\"\n", "    ],\n", "    \"education\": \"BSc Nursing\",\n", "    \"languages\": [\n", "      {\n", "        \"language\": \"German\",\n", "        \"proficiency\": \"B2\"\n", "      }\n", "    ],\n", "    \"match_score\": 85\n", "  },\n", "  {\n", "    \"name\": \"<PERSON>\",\n", "    \"profession\": \"Nurse\",\n", "    \"experience_years\": 5,\n", "    \"skills\": [\n", "      \"ICU\",\n", "      \"ER\"\n", "    ],\n", "    \"education\": \"Diploma in Nursing\",\n", "    \"languages\": [\n", "      {\n", "        \"language\": \"German\",\n", "        \"proficiency\": \"B1\"\n", "      }\n", "    ],\n", "    \"match_score\": 70\n", "  }\n", "]\n"]}], "source": ["# Step 7: Show results as JSO<PERSON>\n", "matched_json = json.dumps(search_candidates(search_input), indent=2, ensure_ascii=False)\n", "print(matched_json)"]}, {"cell_type": "code", "execution_count": null, "id": "21dd0655", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}