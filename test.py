import json
from typing import Dict, List, Optional

from openai import OpenA<PERSON>
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Text, create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

# --------------------
# 1. API & Client Setup
# --------------------
api_key = "********************************************************************************************************************************************************************"
client = OpenAI(api_key=api_key)


# --------------------
# 2. Pydantic Model
# --------------------
class SearchQuery(BaseModel):
    role: str
    specialization: Optional[str] = None
    experience_years: Optional[int] = None
    education: Optional[str] = None
    language: Optional[str] = None
    language_proficiency: Optional[str] = None
    certifications: Optional[List[str]] = []
    endorsements: Optional[List[str]] = []
    licenses: Optional[List[str]] = []


# --------------------
# 3. Database Setup
# --------------------
Base = declarative_base()
engine = create_engine("sqlite:///:memory:")
Session = sessionmaker(bind=engine)
session = Session()


class Candidate(Base):
    __tablename__ = "candidates"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    profession = Column(String)
    experience_years = Column(Integer)
    skills = Column(Text)
    education = Column(String)
    languages = Column(Text)
    certifications = Column(Text)
    endorsements = Column(Text)
    licenses = Column(Text)


Base.metadata.create_all(engine)

# --------------------
# 4. Mock Data
# --------------------
mock_candidates = [
    Candidate(
        name="Anna Müller",
        profession="Nurse",
        experience_years=6,
        skills=json.dumps(["ICU"]),
        education="BSc Nursing",
        languages=json.dumps([{"language": "German", "proficiency": "B2"}]),
        certifications=json.dumps([]),
        endorsements=json.dumps([]),
        licenses=json.dumps([]),
    ),
    Candidate(
        name="John Doe",
        profession="Truck Driver",
        experience_years=3,
        skills=json.dumps(["OTR"]),
        education="High School",
        languages=json.dumps([{"language": "English", "proficiency": "C1"}]),
        certifications=json.dumps(["CDL Class A"]),
        endorsements=json.dumps(["Tanker"]),
        licenses=json.dumps(["Hazmat"]),
    ),
]

session.add_all(mock_candidates)
session.commit()

# --------------------
# 5. Function Schema
# --------------------
function_schema = {
    "name": "extract_candidate_filters",
    "description": "Extracts structured filters for searching candidates from a natural language query.",
    "parameters": {
        "type": "object",
        "properties": {
            "role": {"type": "string"},
            "specialization": {"type": "string"},
            "experience_years": {"type": "integer"},
            "language": {"type": "string"},
            "language_proficiency": {"type": "string"},
            "certifications": {"type": "array", "items": {"type": "string"}},
            "endorsements": {"type": "array", "items": {"type": "string"}},
            "licenses": {"type": "array", "items": {"type": "string"}},
        },
        "required": ["role"],
    },
}

# --------------------
# 6. Query Processing
# --------------------
user_query = "Find me nurses with 5 years experience in ICU with German B2 proficiency"
response = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {
            "role": "system",
            "content": "You extract structured filters for searching candidate records.",
        },
        {"role": "user", "content": user_query},
    ],
    functions=[function_schema],
    function_call={"name": "extract_candidate_filters"},
)

arguments_str = response.choices[0].message.function_call.arguments
function_args = json.loads(arguments_str)
search_input = SearchQuery(**function_args)

# --------------------
# 7. Matching Logic
# --------------------
LANGUAGE_LEVELS = ["A1", "A2", "B1", "B2", "C1", "C2"]
DEFAULT_WEIGHTS = {"skills": 40, "experience": 20, "education": 20, "language": 20}


def redistribute_weights(keys: List[str]) -> Dict[str, float]:
    total = sum(DEFAULT_WEIGHTS[k] for k in keys)
    return {k: round(DEFAULT_WEIGHTS[k] / total * 100, 2) for k in keys}


def language_level_match(candidate_level: str, required_level: str) -> bool:
    try:
        return LANGUAGE_LEVELS.index(candidate_level) >= LANGUAGE_LEVELS.index(
            required_level
        )
    except ValueError:
        return False


def match_candidate(candidate: Candidate, filters: SearchQuery) -> Optional[Dict]:
    active_keys = []
    if filters.specialization:
        active_keys.append("skills")
    if filters.experience_years is not None:
        active_keys.append("experience")
    if filters.education:
        active_keys.append("education")
    if filters.language and filters.language_proficiency:
        active_keys.append("language")
    if not active_keys:
        return None

    weights = redistribute_weights(active_keys)
    score = 0

    # Skills match
    try:
        skills = [s.lower() for s in json.loads(candidate.skills)]
    except:
        skills = []
    if "skills" in active_keys and filters.specialization.lower() in skills:
        score += 100 * (weights["skills"] / 100)

    # Experience match
    if (
        "experience" in active_keys
        and candidate.experience_years >= filters.experience_years
    ):
        score += 100 * (weights["experience"] / 100)

    # Education match
    if (
        "education" in active_keys
        and filters.education.lower() in (candidate.education or "").lower()
    ):
        score += 100 * (weights["education"] / 100)

    # Language match
    try:
        langs = json.loads(candidate.languages)
    except:
        langs = []
    lang_match = any(
        lang["language"].lower() == filters.language.lower()
        and language_level_match(
            lang["proficiency"].upper(), filters.language_proficiency.upper()
        )
        for lang in langs
    )
    if "language" in active_keys and lang_match:
        score += 100 * (weights["language"] / 100)

    score = round(score, 1)
    if score >= 10:
        return {
            "name": candidate.name,
            "profession": candidate.profession,
            "experience_years": candidate.experience_years,
            "skills": skills,
            "education": candidate.education,
            "languages": langs,
            "match_score": score,
        }
    return None


# --------------------
# 8. Candidate Search
# --------------------
def search_candidates(filters: SearchQuery) -> List[Dict]:
    candidates = session.query(Candidate).all()
    results = []
    for c in candidates:
        match = match_candidate(c, filters)
        if match:
            results.append(match)
    return sorted(results, key=lambda x: x["match_score"], reverse=True)[:25]


# --------------------
# 9. Run & Output
# --------------------
matched_json = json.dumps(search_candidates(search_input), indent=2, ensure_ascii=False)
print(matched_json)
