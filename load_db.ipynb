{"cells": [{"cell_type": "code", "execution_count": 5, "id": "1f3dbd54", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>age</th>\n", "      <th>country</th>\n", "      <th>profession</th>\n", "      <th>skills</th>\n", "      <th>education</th>\n", "      <th>experience_years</th>\n", "      <th>language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>47</td>\n", "      <td>Chad</td>\n", "      <td>AI Engineer</td>\n", "      <td>Robotics, Computer Vision, Natural Language Pr...</td>\n", "      <td>B Tech</td>\n", "      <td>5</td>\n", "      <td>French Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>56</td>\n", "      <td>Portugal</td>\n", "      <td>Office Driver</td>\n", "      <td>None</td>\n", "      <td>Certificate Program</td>\n", "      <td>9</td>\n", "      <td>French Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Swaziland</td>\n", "      <td>Office Driver</td>\n", "      <td>None</td>\n", "      <td>B Tech</td>\n", "      <td>1</td>\n", "      <td>English Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>Tunisia</td>\n", "      <td>Software Developer</td>\n", "      <td>C++, Python, JavaScript</td>\n", "      <td>BSc Nursing</td>\n", "      <td>2</td>\n", "      <td>Chinese Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>60</td>\n", "      <td>Tunisia</td>\n", "      <td>Network Administrator</td>\n", "      <td>Troubleshooting, Network Security, Network Design</td>\n", "      <td>High School</td>\n", "      <td>10</td>\n", "      <td>German B1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>495</th>\n", "      <td>496</td>\n", "      <td><PERSON></td>\n", "      <td>33</td>\n", "      <td>Eritrea</td>\n", "      <td>Athelete</td>\n", "      <td>None</td>\n", "      <td>Master's Degree</td>\n", "      <td>17</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>497</td>\n", "      <td><PERSON></td>\n", "      <td>46</td>\n", "      <td>Belize</td>\n", "      <td>Office Driver</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>Chinese Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>498</td>\n", "      <td><PERSON></td>\n", "      <td>46</td>\n", "      <td>Switzerland</td>\n", "      <td>DevOps Engineer</td>\n", "      <td>CI/CD</td>\n", "      <td>BSc Nursing</td>\n", "      <td>15</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>499</td>\n", "      <td><PERSON></td>\n", "      <td>26</td>\n", "      <td>Albania</td>\n", "      <td>Database Administrator</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>20</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>500</td>\n", "      <td><PERSON></td>\n", "      <td>39</td>\n", "      <td>Kiribati</td>\n", "      <td>Social Media Influencer</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>10</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>500 rows × 9 columns</p>\n", "</div>"], "text/plain": ["      id                   name  age      country               profession  \\\n", "0      1    <PERSON><PERSON><PERSON>   47         Chad              AI Engineer   \n", "1      2        <PERSON>   56     Portugal            Office Driver   \n", "2      3       <PERSON>   43    Swaziland            Office Driver   \n", "3      4  <PERSON>   25      Tunisia       Software Developer   \n", "4      5             <PERSON>   60      Tunisia    Network Administrator   \n", "..   ...                    ...  ...          ...                      ...   \n", "495  496      <PERSON>   33      <PERSON><PERSON><PERSON>   \n", "496  497            <PERSON>   46       Belize            Office Driver   \n", "497  498          <PERSON>   46  Switzerland          DevOps Engineer   \n", "498  499             <PERSON>   26      Albania   Database Administrator   \n", "499  500          <PERSON>   39     Kiribati  Social Media Influencer   \n", "\n", "                                                skills            education  \\\n", "0    Robotics, Computer Vision, Natural Language Pr...               B Tech   \n", "1                                                 None  Certificate Program   \n", "2                                                 None               B Tech   \n", "3                              C++, Python, JavaScript          BSc Nursing   \n", "4    Troubleshooting, Network Security, Network Design          High School   \n", "..                                                 ...                  ...   \n", "495                                               None      Master's Degree   \n", "496                                               None                 None   \n", "497                                              CI/CD          BSc Nursing   \n", "498                                               None                 None   \n", "499                                               None                 None   \n", "\n", "     experience_years              language  \n", "0                   5          French Basic  \n", "1                   9         French Fluent  \n", "2                   1  English Intermediate  \n", "3                   2        Chinese Fluent  \n", "4                  10             German B1  \n", "..                ...                   ...  \n", "495                17                  None  \n", "496                 1         Chinese Basic  \n", "497                15         Spanish Basic  \n", "498                20                  None  \n", "499                10         English Basic  \n", "\n", "[500 rows x 9 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import sqlite3\n", "\n", "conn = sqlite3.connect(\"candidates_details_500.db\")\n", "\n", "df = pd.read_sql_query(\"SELECT * FROM candidates\", conn)\n", "\n", "# print(df)\n", "df\n", "\n", "# Optional: Display column names\n", "# print(\"\\nColumns:\", df.columns.tolist())\n", "\n", "# conn.close()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6942faec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>age</th>\n", "      <th>country</th>\n", "      <th>profession</th>\n", "      <th>skills</th>\n", "      <th>education</th>\n", "      <th>experience_years</th>\n", "      <th>language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>57</td>\n", "      <td>Niger</td>\n", "      <td>School Bus Driver</td>\n", "      <td>None</td>\n", "      <td>BSc Nursing</td>\n", "      <td>4</td>\n", "      <td>German C1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>51</td>\n", "      <td>Latvia</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>Commercial License</td>\n", "      <td>15</td>\n", "      <td>German B1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangladesh</td>\n", "      <td>School Bus Driver</td>\n", "      <td>None</td>\n", "      <td>Diploma</td>\n", "      <td>15</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>46</td>\n", "      <td>Norfolk Island</td>\n", "      <td>Truck Driver</td>\n", "      <td>OTR</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>German C2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>31</td>\n", "      <td>Mexico</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>Wound Care, Long-Term Care</td>\n", "      <td>BSc Nursing</td>\n", "      <td>17</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td><PERSON></td>\n", "      <td>31</td>\n", "      <td>Chad</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>High School</td>\n", "      <td>13</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td><PERSON></td>\n", "      <td>27</td>\n", "      <td>Eritrea</td>\n", "      <td>Truck Driver</td>\n", "      <td>OTR</td>\n", "      <td>BSc Nursing</td>\n", "      <td>16</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td><PERSON></td>\n", "      <td>29</td>\n", "      <td>Uruguay</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Diploma</td>\n", "      <td>6</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td><PERSON></td>\n", "      <td>32</td>\n", "      <td>Colombia</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>High School</td>\n", "      <td>5</td>\n", "      <td>German B1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>27</td>\n", "      <td>Norfolk Island</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>BSc Nursing</td>\n", "      <td>9</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td><PERSON></td>\n", "      <td>57</td>\n", "      <td>Guatemala</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>12</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td><PERSON></td>\n", "      <td>40</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>Long-Term Care</td>\n", "      <td>Commercial License</td>\n", "      <td>13</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td><PERSON></td>\n", "      <td>49</td>\n", "      <td>Cape Verde</td>\n", "      <td>Nurse</td>\n", "      <td>None</td>\n", "      <td>Associate Degree</td>\n", "      <td>19</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td><PERSON></td>\n", "      <td>44</td>\n", "      <td>Madagascar</td>\n", "      <td>Nurse</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>12</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>36</td>\n", "      <td>Kuwait</td>\n", "      <td>Truck Driver</td>\n", "      <td>O<PERSON>, Hazmat</td>\n", "      <td>BSc Nursing</td>\n", "      <td>13</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td><PERSON></td>\n", "      <td>54</td>\n", "      <td>Namibia</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>11</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td><PERSON></td>\n", "      <td>37</td>\n", "      <td>Bolivia</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Commercial License</td>\n", "      <td>6</td>\n", "      <td>German C1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>51</td>\n", "      <td>Swaziland</td>\n", "      <td>Nurse</td>\n", "      <td>Emergency, Surgery</td>\n", "      <td>High School</td>\n", "      <td>11</td>\n", "      <td>German C2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td><PERSON></td>\n", "      <td>23</td>\n", "      <td>Chad</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON><PERSON>, OTR</td>\n", "      <td>High School</td>\n", "      <td>3</td>\n", "      <td>Spanish Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td><PERSON></td>\n", "      <td>49</td>\n", "      <td>Tonga</td>\n", "      <td>Nurse</td>\n", "      <td>None</td>\n", "      <td>Associate Degree</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>21</td>\n", "      <td><PERSON></td>\n", "      <td>23</td>\n", "      <td>Panama</td>\n", "      <td>Nurse</td>\n", "      <td>Pediatrics</td>\n", "      <td>Diploma</td>\n", "      <td>2</td>\n", "      <td>German A1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>22</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Guam</td>\n", "      <td>Nurse</td>\n", "      <td>None</td>\n", "      <td>Associate Degree</td>\n", "      <td>14</td>\n", "      <td>English Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>23</td>\n", "      <td><PERSON></td>\n", "      <td>53</td>\n", "      <td>North Macedonia</td>\n", "      <td>Nurse</td>\n", "      <td>Emergency, ICU</td>\n", "      <td>BSc Nursing</td>\n", "      <td>7</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>24</td>\n", "      <td><PERSON></td>\n", "      <td>36</td>\n", "      <td>Monaco</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>12</td>\n", "      <td>Spanish Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>25</td>\n", "      <td><PERSON></td>\n", "      <td>26</td>\n", "      <td>Guinea</td>\n", "      <td>Nurse</td>\n", "      <td>Pediatrics</td>\n", "      <td>BSc Nursing</td>\n", "      <td>3</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>26</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Sao Tome and Principe</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON>er</td>\n", "      <td>Commercial License</td>\n", "      <td>20</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>27</td>\n", "      <td><PERSON></td>\n", "      <td>36</td>\n", "      <td>Heard Island and McDonald Islands</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>Long-Term Care, Wound Care</td>\n", "      <td>Diploma</td>\n", "      <td>17</td>\n", "      <td>English Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>28</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Chad</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>Long-Term Care, Wound Care</td>\n", "      <td>None</td>\n", "      <td>4</td>\n", "      <td>Spanish Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>29</td>\n", "      <td><PERSON></td>\n", "      <td>60</td>\n", "      <td>Saint <PERSON> and Miquelon</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Diploma</td>\n", "      <td>16</td>\n", "      <td>German C2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>30</td>\n", "      <td><PERSON></td>\n", "      <td>22</td>\n", "      <td>Slovakia (Slovak Republic)</td>\n", "      <td>Truck Driver</td>\n", "      <td>OTR</td>\n", "      <td>BSc Nursing</td>\n", "      <td>2</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td><PERSON></td>\n", "      <td>39</td>\n", "      <td>Italy</td>\n", "      <td>Nurse</td>\n", "      <td>None</td>\n", "      <td>Diploma</td>\n", "      <td>14</td>\n", "      <td>German C1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>32</td>\n", "      <td><PERSON></td>\n", "      <td>33</td>\n", "      <td>Singapore</td>\n", "      <td>School Bus Driver</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>German B1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>33</td>\n", "      <td><PERSON></td>\n", "      <td>23</td>\n", "      <td><PERSON></td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Associate Degree</td>\n", "      <td>20</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>34</td>\n", "      <td><PERSON></td>\n", "      <td>36</td>\n", "      <td>Malawi</td>\n", "      <td>Truck Driver</td>\n", "      <td>OTR</td>\n", "      <td>High School</td>\n", "      <td>11</td>\n", "      <td>English Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>35</td>\n", "      <td><PERSON></td>\n", "      <td>51</td>\n", "      <td>Kazakhstan</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>Wound Care</td>\n", "      <td>BSc Nursing</td>\n", "      <td>4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>36</td>\n", "      <td><PERSON></td>\n", "      <td>44</td>\n", "      <td>Zambia</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>Associate Degree</td>\n", "      <td>19</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>37</td>\n", "      <td><PERSON></td>\n", "      <td>28</td>\n", "      <td>Romania</td>\n", "      <td>School Bus Driver</td>\n", "      <td>None</td>\n", "      <td>Diploma</td>\n", "      <td>20</td>\n", "      <td>Spanish Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>38</td>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Rwanda</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON>, OTR</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>German A1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>39</td>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Tajikistan</td>\n", "      <td>Nurse</td>\n", "      <td>Pediatrics, Emergency</td>\n", "      <td>Diploma</td>\n", "      <td>4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>40</td>\n", "      <td><PERSON></td>\n", "      <td>31</td>\n", "      <td>Western Sahara</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Diploma</td>\n", "      <td>8</td>\n", "      <td>English Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>41</td>\n", "      <td><PERSON></td>\n", "      <td>54</td>\n", "      <td>Sudan</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON><PERSON>, OTR</td>\n", "      <td>BSc Nursing</td>\n", "      <td>12</td>\n", "      <td>Spanish Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>42</td>\n", "      <td><PERSON></td>\n", "      <td>51</td>\n", "      <td>Kenya</td>\n", "      <td>Nurse</td>\n", "      <td>Emergency, ICU, Surgery, Pediatrics</td>\n", "      <td>Commercial License</td>\n", "      <td>14</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>43</td>\n", "      <td><PERSON></td>\n", "      <td>23</td>\n", "      <td>United States of America</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>None</td>\n", "      <td>12</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>44</td>\n", "      <td><PERSON></td>\n", "      <td>54</td>\n", "      <td>Jordan</td>\n", "      <td>Nurse</td>\n", "      <td>Emergency, Pediatrics</td>\n", "      <td>Associate Degree</td>\n", "      <td>11</td>\n", "      <td>German A1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>45</td>\n", "      <td><PERSON></td>\n", "      <td>28</td>\n", "      <td>Lao People's Democratic Republic</td>\n", "      <td>Truck Driver</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>9</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>46</td>\n", "      <td><PERSON></td>\n", "      <td>23</td>\n", "      <td>Syrian Arab Republic</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er</td>\n", "      <td>Associate Degree</td>\n", "      <td>19</td>\n", "      <td>English Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>47</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Myanmar</td>\n", "      <td>Truck Driver</td>\n", "      <td><PERSON><PERSON>, OTR</td>\n", "      <td>Diploma</td>\n", "      <td>1</td>\n", "      <td>German A2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>48</td>\n", "      <td><PERSON></td>\n", "      <td>31</td>\n", "      <td>Gibraltar</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>High School</td>\n", "      <td>19</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>49</td>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Netherlands Antilles</td>\n", "      <td>Licensed Practical Nurse</td>\n", "      <td>None</td>\n", "      <td>High School</td>\n", "      <td>14</td>\n", "      <td>Spanish Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>50</td>\n", "      <td><PERSON></td>\n", "      <td>36</td>\n", "      <td>Trinidad and Tobago</td>\n", "      <td>School Bus Driver</td>\n", "      <td>Passenger Endorsement</td>\n", "      <td>Diploma</td>\n", "      <td>19</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id                 name  age                            country  \\\n", "0    1            <PERSON>   57                              Niger   \n", "1    2          <PERSON>   51                             Latvia   \n", "2    3            <PERSON>   25                         Bangladesh   \n", "3    4         <PERSON>   46                     Norfolk Island   \n", "4    5      <PERSON>   31                             Mexico   \n", "5    6         Jordan Allen   31                               Chad   \n", "6    7      <PERSON>   27                            Eritrea   \n", "7    8      Alejandro <PERSON>   29                            Uruguay   \n", "8    9           <PERSON>   32                           Colombia   \n", "9   10        <PERSON><PERSON><PERSON>   27                     Norfolk Island   \n", "10  11        <PERSON>   57                          Guatemala   \n", "11  12       <PERSON>   40                              <PERSON><PERSON>   \n", "12  13          <PERSON>   49                         Cape Verde   \n", "13  14       <PERSON>   44                         Madagascar   \n", "14  15         <PERSON>   36                             Kuwait   \n", "15  16       <PERSON>   54                            Namibia   \n", "16  17     <PERSON>   37                            Bolivia   \n", "17  18         <PERSON><PERSON>   51                          Swaziland   \n", "18  19       <PERSON>   23                               <PERSON>   \n", "19  20   <PERSON> Henderson   49                              Tonga   \n", "20  21        <PERSON>   23                             Panama   \n", "21  22        <PERSON>   43                               Guam   \n", "22  23       Kelsey <PERSON>   53                    North Macedonia   \n", "23  24       <PERSON>   36                             Monaco   \n", "24  25          <PERSON>   26                             Guinea   \n", "25  26        <PERSON>   43              Sao Tome and <PERSON><PERSON><PERSON><PERSON>   \n", "26  27      <PERSON>   36  Heard Island and McDonald Islands   \n", "27  28    <PERSON>   43                               <PERSON>   \n", "28  29           <PERSON>   60          Saint Pierre and <PERSON><PERSON><PERSON>   \n", "29  30      <PERSON>   22         Slovakia (Slovak Republic)   \n", "30  31         <PERSON>   39                              Italy   \n", "31  32          <PERSON>   33                          Singapore   \n", "32  33            <PERSON>   23                       <PERSON>   \n", "33  34         <PERSON>   36                             Malawi   \n", "34  35           <PERSON>   51                         Kazakhstan   \n", "35  36     <PERSON>   44                             Zambia   \n", "36  37      <PERSON>   28                            Romania   \n", "37  38           <PERSON>   35                             Rwanda   \n", "38  39        <PERSON>   35                         Tajikistan   \n", "39  40            <PERSON>   31                     Western Sahara   \n", "40  41        <PERSON>   54                              Sudan   \n", "41  42      <PERSON>   51                              Kenya   \n", "42  43         <PERSON>   23           United States of America   \n", "43  44          <PERSON>   54                             <PERSON>   \n", "44  45  <PERSON>   28   Lao People's Democratic Republic   \n", "45  46        <PERSON>   23               Syrian Arab Republic   \n", "46  47        <PERSON>   43                            Myanmar   \n", "47  48   <PERSON>   31                          Gibraltar   \n", "48  49     <PERSON>   35               Netherlands Antilles   \n", "49  50        <PERSON>   36                Trinidad and Tobago   \n", "\n", "                  profession                               skills  \\\n", "0          School Bus Driver                                 None   \n", "1   Licensed Practical Nurse                                 None   \n", "2          School Bus Driver                                 None   \n", "3               Truck Driver                                  OTR   \n", "4   Licensed Practical Nurse           Wound Care, Long-Term Care   \n", "5          School Bus Driver                Passenger Endorsement   \n", "6               Truck Driver                                  OTR   \n", "7          School Bus Driver                Passenger Endorsement   \n", "8   Licensed Practical Nurse                                 None   \n", "9          School Bus Driver                Passenger Endorsement   \n", "10  Licensed Practical Nurse                                 None   \n", "11  Licensed Practical Nurse                       Long-Term Care   \n", "12                     Nurse                                 None   \n", "13                     Nurse                                 None   \n", "14              Truck Driver                          OTR, Hazmat   \n", "15  Licensed Practical Nurse                                 None   \n", "16         School Bus Driver                Passenger Endorsement   \n", "17                     Nurse                   Emergency, Surgery   \n", "18              Truck Driver                          Tanker, OTR   \n", "19                     Nurse                                 None   \n", "20                     Nurse                           Pediatrics   \n", "21                     Nurse                                 None   \n", "22                     Nurse                       Emergency, ICU   \n", "23  Licensed Practical Nurse                                 None   \n", "24                     Nurse                           Pediatrics   \n", "25              Truck Driver                               Tanker   \n", "26  Licensed Practical Nurse           Long-Term Care, Wound Care   \n", "27  Licensed Practical Nurse           Long-Term Care, Wound Care   \n", "28         School Bus Driver                Passenger Endorsement   \n", "29              Truck Driver                                  OTR   \n", "30                     Nurse                                 None   \n", "31         School Bus Driver                                 None   \n", "32         School Bus Driver                Passenger Endorsement   \n", "33              Truck Driver                                  OTR   \n", "34  Licensed Practical Nurse                           Wound Care   \n", "35  Licensed Practical Nurse                                 None   \n", "36         School Bus Driver                                 None   \n", "37              Truck Driver                  <PERSON>, <PERSON><PERSON>, O<PERSON>   \n", "38                     Nurse                Pediatrics, Emergency   \n", "39         School Bus Driver                Passenger Endorsement   \n", "40              Truck Driver                          Tanker, OTR   \n", "41                     Nurse  Emergency, ICU, Surgery, Pediatrics   \n", "42         School Bus Driver                Passenger Endorsement   \n", "43                     Nurse                Emergency, Pediatrics   \n", "44              Truck Driver                                 None   \n", "45              Truck Driver                  <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>   \n", "46              Truck Driver                          Tanker, OTR   \n", "47         School Bus Driver                Passenger Endorsement   \n", "48  Licensed Practical Nurse                                 None   \n", "49         School Bus Driver                Passenger Endorsement   \n", "\n", "             education  experience_years              language  \n", "0          BSc Nursing                 4             German C1  \n", "1   Commercial License                15             German B1  \n", "2              Diploma                15                  None  \n", "3                 None                18             German C2  \n", "4          BSc Nursing                17         Spanish Basic  \n", "5          High School                13         English Basic  \n", "6          BSc Nursing                16         English Basic  \n", "7              Diploma                 6         Spanish Basic  \n", "8          High School                 5             German B1  \n", "9          BSc Nursing                 9                  None  \n", "10                None                12                  None  \n", "11  Commercial License                13        English Fluent  \n", "12    Associate Degree                19         English Basic  \n", "13                None                12         English Basic  \n", "14         BSc Nursing                13         English Basic  \n", "15                None                11         Spanish Basic  \n", "16  Commercial License                 6             German C1  \n", "17         High School                11             German C2  \n", "18         High School                 3        Spanish Fluent  \n", "19    Associate Degree                 2                  None  \n", "20             Diploma                 2             German A1  \n", "21    Associate Degree                14  English Intermediate  \n", "22         BSc Nursing                 7        English Fluent  \n", "23                None                12  Spanish Intermediate  \n", "24         BSc Nursing                 3         Spanish Basic  \n", "25  Commercial License                20        English Fluent  \n", "26             Diploma                17         English Basic  \n", "27                None                 4         Spanish Basic  \n", "28             Diploma                16             German C2  \n", "29         BSc Nursing                 2        English Fluent  \n", "30             Diploma                14             German C1  \n", "31                None                18             German B1  \n", "32    Associate Degree                20        English Fluent  \n", "33         High School                11  English Intermediate  \n", "34         BSc Nursing                 4                  None  \n", "35    Associate Degree                19                  None  \n", "36             Diploma                20  Spanish Intermediate  \n", "37                None                 2             German A1  \n", "38             Diploma                 4                  None  \n", "39             Diploma                 8  English Intermediate  \n", "40         BSc Nursing                12        Spanish Fluent  \n", "41  Commercial License                14                  None  \n", "42                None                12                  None  \n", "43    Associate Degree                11             German A1  \n", "44                None                 9                  None  \n", "45    Associate Degree                19        English Fluent  \n", "46             Diploma                 1             German A2  \n", "47         High School                19                  None  \n", "48         High School                14        Spanish Fluent  \n", "49             Diploma                19                  None  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(50)"]}, {"cell_type": "code", "execution_count": 6, "id": "fd84d7bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 500 entries, 0 to 499\n", "Data columns (total 9 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   id                500 non-null    int64 \n", " 1   name              500 non-null    object\n", " 2   age               500 non-null    int64 \n", " 3   country           500 non-null    object\n", " 4   profession        500 non-null    object\n", " 5   skills            164 non-null    object\n", " 6   education         403 non-null    object\n", " 7   experience_years  500 non-null    int64 \n", " 8   language          394 non-null    object\n", "dtypes: int64(3), object(6)\n", "memory usage: 35.3+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "e796d6f7", "metadata": {}, "outputs": [], "source": ["conn.close()"]}, {"cell_type": "code", "execution_count": 1, "id": "a3b5243c", "metadata": {}, "outputs": [], "source": ["import sqlite3"]}, {"cell_type": "code", "execution_count": 5, "id": "8677bec1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Connection at 0x7ddb52e8dd50>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["connection = sqlite3.connect(\"databases/candidates_details_500.db\")\n", "connection"]}, {"cell_type": "code", "execution_count": 6, "id": "cad4b1bd", "metadata": {}, "outputs": [], "source": ["cursor=connection.cursor()"]}, {"cell_type": "code", "execution_count": 7, "id": "d5e68b13", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Cursor at 0x7ddb52c38cc0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["cursor"]}, {"cell_type": "code", "execution_count": 8, "id": "144f8b32", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Cursor at 0x7ddb52c38cc0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cursor.execute(\"select * from candidates;\")"]}, {"cell_type": "code", "execution_count": 10, "id": "6c98a2ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(('id', None, None, None, None, None, None), ('name', None, None, None, None, None, None), ('age', None, None, None, None, None, None), ('country', None, None, None, None, None, None), ('profession', None, None, None, None, None, None), ('skills', None, None, None, None, None, None), ('education', None, None, None, None, None, None), ('experience_years', None, None, None, None, None, None), ('language', None, None, None, None, None, None))\n"]}], "source": ["# print the table name from the cursor\n", "\n", "print(cursor.description)"]}, {"cell_type": "code", "execution_count": 9, "id": "7f95b297", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, '<PERSON><PERSON><PERSON>', 47, '<PERSON>', 'AI Engineer', 'Robotics, Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 5, 'French Basic')\n", "(2, '<PERSON>', 56, 'Portugal', 'Office Driver', None, 'Certificate Program', 9, 'French Fluent')\n", "(3, '<PERSON>', 43, 'Swaziland', 'Office Driver', None, 'B Tech', 1, 'English Intermediate')\n", "(4, '<PERSON>', 25, 'Tunisia', 'Software Developer', 'C++, Python, JavaScript', 'BSc Nursing', 2, 'Chinese Fluent')\n", "(5, '<PERSON>', 60, 'Tunisia', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'High School', 10, 'German B1')\n", "(6, '<PERSON>', 30, 'Sri Lanka', 'Office Driver', None, None, 8, 'Spanish Intermediate')\n", "(7, '<PERSON>', 49, 'Bosnia and Herzegovina', '<PERSON>', <PERSON>, \"Master's Degree\", 19, None)\n", "(8, '<PERSON>', 31, 'Nicaragua', 'Cybersecurity Specialist', 'Incident Response', \"Master's Degree\", 3, 'German C2')\n", "(9, '<PERSON>', 32, 'Burundi', 'Engineer', None, 'BSc Nursing', 5, None)\n", "(10, '<PERSON>', 37, '<PERSON>', '<PERSON>er', <PERSON>, \"Bachelor's Degree\", 9, 'English Fluent')\n", "(11, '<PERSON>', 53, 'Nigeria', 'Movie Director', None, 'Associate Degree', 13, 'Spanish Basic')\n", "(12, '<PERSON>', 43, '<PERSON>', 'Nurse', 'Wound Care, Long-Term Care, Emergency', \"Master's Degree\", 16, 'Chinese Basic')\n", "(13, '<PERSON>', 51, 'Bangladesh', 'Data Scientist', 'Data Analysis', \"Master's Degree\", 3, 'Spanish Intermediate')\n", "(14, '<PERSON>', 49, 'Dominican Republic', 'Teacher', None, 'High School', 5, 'Chinese Basic')\n", "(15, '<PERSON>', 50, 'Guam', 'Movie Director', <PERSON>, 'BSc Nursing', 10, 'Chinese Basic')\n", "(16, '<PERSON>', 56, 'Afghanistan', 'Test Engineer', None, None, 15, 'Spanish Intermediate')\n", "(17, '<PERSON>', 47, '<PERSON><PERSON>', 'Movie Director', None, 'P<PERSON>', 14, None)\n", "(18, '<PERSON>', 42, 'Bosnia and Herzegovina', 'UX/UI Designer', 'Wireframing', 'Certificate Program', 5, None)\n", "(19, '<PERSON>', 56, 'Pakistan', 'Engineer', None, None, 5, 'German B2')\n", "(20, '<PERSON>', 39, 'Russian Federation', 'Cricketer', None, 'Commercial License', 3, None)\n", "(21, '<PERSON>', 54, '<PERSON>', 'Nurse', 'ICU, Pediatrics, Long-Term Care, Emergency, Surgery', 'High School', 9, 'French Fluent')\n", "(22, '<PERSON>', 34, 'Grenada', 'Professor', <PERSON>, 'B Tech', 12, 'Spanish Intermediate')\n", "(23, '<PERSON>', 51, '<PERSON>', '<PERSON>', None, 'M <PERSON>', 8, None)\n", "(24, '<PERSON>', 59, 'Somalia', 'Software Developer', None, 'M Tech', 7, 'Chinese Intermediate')\n", "(25, '<PERSON>', 48, 'Svalbard & Jan Mayen Islands', 'Teacher', None, \"Master's Degree\", 10, 'Spanish Basic')\n", "(26, '<PERSON>', 57, 'Ecuador', 'Movie Director', None, 'M Tech', 15, None)\n", "(27, '<PERSON>', 58, 'Bhutan', 'Database Administrator', None, 'Certificate Program', 1, None)\n", "(28, '<PERSON>', 27, 'Dominican Republic', '<PERSON><PERSON> Trainer', None, None, 17, 'English Fluent')\n", "(29, '<PERSON>', 44, 'Sierra Leone', '<PERSON><PERSON> Trainer', None, None, 19, 'English Basic')\n", "(30, '<PERSON>', 54, 'Sri Lanka', 'Movie Director', None, None, 19, 'English Intermediate')\n", "(31, '<PERSON>', 54, 'Aruba', 'Movie Director', None, 'Commercial License', 2, 'French Intermediate')\n", "(32, '<PERSON>', 54, '<PERSON>', '<PERSON><PERSON><PERSON>', None, '<PERSON> <PERSON>', 20, 'English Intermediate')\n", "(33, '<PERSON><PERSON>', 34, 'Svalbard & Jan Mayen Islands', 'UX/UI Designer', 'Prototyping, Wireframing', None, 18, 'French Fluent')\n", "(34, '<PERSON>', 54, '<PERSON>', '<PERSON><PERSON>', <PERSON>, \"Bachelor's Degree\", 18, 'English Fluent')\n", "(35, '<PERSON>', 46, 'Pakistan', 'Licensed Practical Nurse', None, 'M Tech', 19, None)\n", "(36, '<PERSON>', 27, 'Bangladesh', 'Product Manager', 'Project Management, Market Research, Product Development', \"Master's Degree\", 15, 'Spanish Intermediate')\n", "(37, '<PERSON>', 26, 'Aruba', '<PERSON><PERSON>', <PERSON>, \"Master's Degree\", 10, 'German A2')\n", "(38, '<PERSON>', 44, 'Hong Kong', 'Movie Director', None, None, 7, 'Spanish Intermediate')\n", "(39, '<PERSON>', 25, 'Russian Federation', 'Professor', None, 'Commercial License', 19, 'English Basic')\n", "(40, '<PERSON>', 44, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>er', <PERSON>, 'Commercial License', 18, 'English Fluent')\n", "(41, '<PERSON>', 39, 'New Caledonia', '<PERSON>', None, 'Certificate Program', 18, 'English Basic')\n", "(42, '<PERSON>', 33, '<PERSON>', 'Movie Producer', <PERSON>, 'Associate Degree', 10, 'German C1')\n", "(43, '<PERSON>', 48, 'Pakistan', 'Professor', None, 'Diploma', 6, 'English Intermediate')\n", "(44, '<PERSON>', 25, 'Norfolk Island', '<PERSON>', None, None, 12, 'Spanish Intermediate')\n", "(45, '<PERSON>', 40, '<PERSON>', 'Test Engineer', None, 'M Tech', 20, None)\n", "(46, '<PERSON>', 54, '<PERSON> Vincent and the Grenadines', 'School Bus Driver', None, 'BSc Nursing', 13, 'Spanish Intermediate')\n", "(47, '<PERSON>', 28, 'Gibraltar', 'Engineer', 'Civil Engineering, Chemical Engineering, Electrical Engineering, Mechanical Engineering', None, 11, 'Spanish Fluent')\n", "(48, '<PERSON>', 22, 'Puerto Rico', '<PERSON><PERSON>er', <PERSON>, \"Bachelor's Degree\", 17, 'Spanish Basic')\n", "(49, '<PERSON>', 51, 'British Virgin Islands', 'Teacher', None, 'Diploma', 2, 'English Intermediate')\n", "(50, '<PERSON>', 40, '<PERSON><PERSON><PERSON>', 'Test Engineer', None, 'B Tech', 19, 'French Intermediate')\n", "(51, '<PERSON>', 22, 'Mexico', '<PERSON>', <PERSON>, \"Master's Degree\", 6, 'English Intermediate')\n", "(52, '<PERSON>', 51, 'Bermuda', 'Engineer', 'Civil Engineering, Chemical Engineering', None, 11, 'Spanish Intermediate')\n", "(53, '<PERSON>', 42, '<PERSON>', '<PERSON>', None, 'Diploma', 11, 'French Basic')\n", "(54, '<PERSON>', 57, 'Sao Tome and Principe', 'DevOps Engineer', 'Cloud Computing, Containerization', 'Commercial License', 10, None)\n", "(55, '<PERSON>', 52, '<PERSON>', '<PERSON><PERSON> Trainer', None, 'Commercial License', 12, 'Chinese Basic')\n", "(56, '<PERSON><PERSON><PERSON>', 41, 'Libyan Arab <PERSON>', 'Social Media Influencer', None, None, 9, 'German C2')\n", "(57, '<PERSON>', 49, 'South Africa', '<PERSON><PERSON>', None, 'BSc Nursing', 15, None)\n", "(58, '<PERSON>', 58, 'Cape Verde', 'Professor', <PERSON>, \"Bachelor's Degree\", 16, 'Spanish Basic')\n", "(59, '<PERSON>', 26, \"Lao People's Democratic Republic\", 'Social Media Influencer', None, \"Bachelor's Degree\", 4, 'French Fluent')\n", "(60, '<PERSON>', 26, '<PERSON>', 'Office Driver', None, None, 6, None)\n", "(61, '<PERSON>', 58, 'Northern Mariana Islands', 'Test Engineer', None, None, 19, None)\n", "(62, '<PERSON>', 29, 'Ireland', 'Product Manager', 'Project Management, Product Development, Market Research', 'High School', 16, None)\n", "(63, '<PERSON>', 22, 'Hungary', 'Software Developer', None, 'BSc Nursing', 9, 'German B2')\n", "(64, '<PERSON><PERSON>', 50, '<PERSON>', '<PERSON>', None, 'B Tech', 18, None)\n", "(65, '<PERSON>', 32, 'Malta', 'Cybersecurity Specialist', None, None, 1, 'Chinese Intermediate')\n", "(66, '<PERSON>', 22, '<PERSON>', 'Professor', None, None, 2, 'Spanish Intermediate')\n", "(67, '<PERSON>', 52, 'Greenland', 'Database Administrator', 'Backup and Recovery', 'BSc Nursing', 2, 'English Basic')\n", "(68, '<PERSON>', 26, 'Western Sahara', 'Office Driver', <PERSON>, 'BSc Nursing', 9, 'English Basic')\n", "(69, '<PERSON>', 25, 'Lithuania', 'Movie Producer', None, 'High School', 9, 'German B1')\n", "(70, '<PERSON>', 60, '<PERSON> Kitts and Nevis', '<PERSON>', None, 'B <PERSON>', 1, None)\n", "(71, '<PERSON>', 34, '<PERSON>', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'M Tech', 17, 'English Intermediate')\n", "(72, '<PERSON>', 28, 'Guam', 'Data Scientist', 'Statistics, Machine Learning, Data Analysis, Data Visualization', 'M Tech', 16, 'Spanish Fluent')\n", "(73, 'Heidi House', 58, 'United Kingdom', 'Nurse', 'Pediatrics, Surgery', 'BSc Nursing', 12, None)\n", "(74, '<PERSON>', 36, '<PERSON><PERSON><PERSON>', '<PERSON>', <PERSON>, 'PHD', 1, 'Chinese Fluent')\n", "(75, '<PERSON>', 55, '<PERSON><PERSON>', 'Database Administrator', 'Performance Tuning, Backup and Recovery, Database Design', None, 16, 'French Basic')\n", "(76, '<PERSON>', 43, 'United Arab Emirates', '<PERSON><PERSON><PERSON>', None, 'BSc Nursing', 17, 'French Fluent')\n", "(77, '<PERSON>', 32, 'United States Minor Outlying Islands', 'Engineer', 'Electrical Engineering, Mechanical Engineering, Chemical Engineering', None, 5, 'German C2')\n", "(78, '<PERSON>', 53, 'Cuba', 'Product Manager', 'Product Development, Market Research', 'PHD', 2, None)\n", "(79, '<PERSON>', 51, 'Norway', 'Engineer', 'Mechanical Engineering, Chemical Engineering, Civil Engineering, Electrical Engineering', 'PHD', 9, 'Chinese Fluent')\n", "(80, '<PERSON>', 47, '<PERSON>', '<PERSON>', None, None, 16, 'Chinese Basic')\n", "(81, '<PERSON><PERSON>', 47, 'Cambodia', 'Software Developer', 'JavaScript, C++, Python, Java, Agile Development', 'Diploma', 15, 'French Basic')\n", "(82, '<PERSON>', 60, 'Grenada', 'Software Developer', None, 'Diploma', 5, 'English Fluent')\n", "(83, '<PERSON>', 59, '<PERSON><PERSON>', '<PERSON>', None, 'B Tech', 17, 'German A1')\n", "(84, '<PERSON>', 39, 'Egypt', '<PERSON>', None, None, 15, 'German B2')\n", "(85, '<PERSON>', 30, 'Vietnam', 'Licensed Practical Nurse', 'Medication Administration', None, 11, 'Spanish Intermediate')\n", "(86, '<PERSON>', 51, '<PERSON>', '<PERSON><PERSON>er', <PERSON>, 'BSc Nursing', 10, 'Chinese Fluent')\n", "(87, '<PERSON>', 44, 'Barbados', '<PERSON>', <PERSON>, 'PHD', 10, 'Chinese Basic')\n", "(88, '<PERSON>', 36, 'Haiti', 'Network Administrator', None, None, 3, None)\n", "(89, '<PERSON>', 36, 'Tanzania', '<PERSON>', None, 'BSc Nursing', 9, None)\n", "(90, '<PERSON>', 25, 'Moldova', 'Social Media Influencer', None, 'Certificate Program', 11, None)\n", "(91, '<PERSON><PERSON>', 25, '<PERSON>', 'AI Engineer', 'Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 11, None)\n", "(92, '<PERSON>', 28, 'Singapore', '<PERSON>', <PERSON>, \"Master's Degree\", 3, 'Chinese Basic')\n", "(93, '<PERSON>', 27, 'Brazil', 'Cybersecurity Specialist', None, None, 13, 'Chinese Basic')\n", "(94, '<PERSON>', 49, 'Qatar', 'Cybersecurity Specialist', None, 'Certificate Program', 6, 'Chinese Intermediate')\n", "(95, '<PERSON>', 40, 'New Zealand', 'Product Manager', 'Project Management, Market Research', 'Commercial License', 8, 'German C2')\n", "(96, '<PERSON>', 56, '<PERSON><PERSON><PERSON>', '<PERSON>', None, None, 5, 'German C2')\n", "(97, '<PERSON>', 22, 'Grenada', '<PERSON>', <PERSON>, 'Diploma', 6, 'Spanish Intermediate')\n", "(98, '<PERSON>', 49, 'American Samoa', 'Test Engineer', None, \"Bachelor's Degree\", 4, 'Spanish Fluent')\n", "(99, '<PERSON>', 51, 'Holy See (Vatican City State)', 'Engineer', None, 'PHD', 19, 'English Basic')\n", "(100, '<PERSON>', 45, 'Trinidad and Tobago', 'Dancer', None, 'Diploma', 11, 'French Basic')\n", "(101, '<PERSON>', 60, 'Niger', 'Software Developer', 'Agile Development', 'M Tech', 7, 'English Intermediate')\n", "(102, '<PERSON>', 40, '<PERSON>', 'Movie Producer', None, \"Master's Degree\", 17, None)\n", "(103, '<PERSON>', 23, 'Suriname', '<PERSON><PERSON><PERSON>', None, 'BSc Nursing', 15, 'French Fluent')\n", "(104, '<PERSON>', 27, 'Faroe Islands', 'Product Manager', None, 'Commercial License', 19, 'Spanish Fluent')\n", "(105, '<PERSON>', 24, '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', None, None, 18, None)\n", "(106, '<PERSON>', 59, 'Czech Republic', 'Licensed Practical Nurse', None, \"Master's Degree\", 2, 'German C2')\n", "(107, '<PERSON>', 46, 'United Kingdom', 'UX/UI Designer', 'Usability Testing, User Research', \"Bachelor's Degree\", 14, 'Chinese Intermediate')\n", "(108, '<PERSON>', 30, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, None, 16, 'German B1')\n", "(109, '<PERSON>', 46, 'Iran', 'School Bus Driver', 'Defensive Driving', 'PHD', 5, 'French Intermediate')\n", "(110, '<PERSON>', 25, 'Cuba', 'Movie Director', None, 'BSc Nursing', 7, 'German B1')\n", "(111, '<PERSON>', 23, 'Kenya', 'Data Scientist', 'Statistics, Data Analysis, Machine Learning', 'Diploma', 18, 'Chinese Fluent')\n", "(112, '<PERSON>', 26, 'Iceland', 'Cybersecurity Specialist', 'Compliance, Incident Response, Threat Analysis', 'M Tech', 20, 'English Fluent')\n", "(113, '<PERSON>', 49, '<PERSON><PERSON><PERSON>', 'Database Administrator', None, None, 12, 'Chinese Fluent')\n", "(114, '<PERSON>', 52, 'Antarctica (the territory South of 60 deg S)', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'PHD', 6, 'German A2')\n", "(115, '<PERSON>', 54, 'Vanuatu', 'UX/UI Designer', None, None, 6, 'Spanish Fluent')\n", "(116, '<PERSON>', 43, '<PERSON>', '<PERSON>', None, 'Commercial License', 8, 'English Fluent')\n", "(117, '<PERSON><PERSON><PERSON>', 45, 'Hong Kong', '<PERSON>', None, None, 2, 'German A2')\n", "(118, '<PERSON>', 23, 'Djibouti', '<PERSON>', None, 'Commercial License', 3, 'German C2')\n", "(119, '<PERSON>', 28, 'French Southern Territories', '<PERSON><PERSON> Trainer', None, 'High School', 1, 'German C1')\n", "(120, '<PERSON>', 24, '<PERSON><PERSON><PERSON>', 'AI Engineer', None, None, 17, None)\n", "(121, '<PERSON>', 30, 'Italy', '<PERSON><PERSON>', None, None, 1, 'French Fluent')\n", "(122, '<PERSON>', 38, 'Burundi', '<PERSON>', <PERSON>, \"Master's Degree\", 7, 'Chinese Basic')\n", "(123, '<PERSON>', 43, 'New Zealand', 'UX/UI Designer', 'Prototyping, User Research', 'Certificate Program', 3, 'Chinese Intermediate')\n", "(124, '<PERSON>', 58, 'Western Sahara', 'Software Developer', 'Python, Java, JavaScript', 'PHD', 13, 'English Fluent')\n", "(125, '<PERSON>', 50, 'Ghana', 'Nurse', 'Pediatrics', \"Master's Degree\", 10, 'Chinese Fluent')\n", "(126, '<PERSON>', 32, 'Yemen', 'DevOps Engineer', 'Containerization, Cloud Computing', \"Bachelor's Degree\", 18, 'French Fluent')\n", "(127, '<PERSON>', 34, 'Bahamas', '<PERSON>', None, \"Bachelor's Degree\", 15, None)\n", "(128, '<PERSON>', 33, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', None, 'P<PERSON>', 17, 'English Basic')\n", "(129, '<PERSON>', 26, 'Isle of Man', 'Truck Driver', '<PERSON><PERSON>, Tanker', 'BSc Nursing', 4, 'English Intermediate')\n", "(130, '<PERSON>', 44, 'Angola', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'Associate Degree', 4, 'Spanish Intermediate')\n", "(131, '<PERSON>', 31, 'Mozambique', 'Data Scientist', 'Statistics, Machine Learning', 'PHD', 13, 'English Basic')\n", "(132, '<PERSON>', 49, '<PERSON><PERSON>', 'Test Engineer', None, None, 6, None)\n", "(133, '<PERSON>', 43, '<PERSON> Pierre and <PERSON>', 'AI Engineer', 'Deep Learning, Natural Language Processing, Robotics', 'B Tech', 10, 'Spanish Basic')\n", "(134, '<PERSON>', 31, '<PERSON><PERSON><PERSON>', '<PERSON>', <PERSON>, 'High School', 11, 'English Basic')\n", "(135, '<PERSON>', 43, 'Estonia', 'Database Administrator', 'Database Design', \"Bachelor's Degree\", 7, 'Spanish Fluent')\n", "(136, '<PERSON><PERSON>', 25, 'Uganda', 'Software Developer', 'JavaScript', 'Commercial License', 11, 'German C2')\n", "(137, '<PERSON>', 50, 'Burundi', 'UX/UI Designer', 'Prototyping, Usability Testing', 'BSc Nursing', 16, 'French Fluent')\n", "(138, '<PERSON>', 33, 'Romania', 'Network Administrator', 'Troubleshooting, Network Design', None, 6, 'German C1')\n", "(139, '<PERSON>', 49, '<PERSON>', 'Professor', None, None, 10, 'Chinese Basic')\n", "(140, '<PERSON>', 48, 'Central African Republic', 'AI Engineer', None, 'PHD', 10, 'Chinese Fluent')\n", "(141, 'Crystal David DVM', 42, 'French Polynesia', 'UX/UI Designer', 'Usability Testing, User Research, Wireframing, Prototyping', 'Commercial License', 13, 'Spanish Fluent')\n", "(142, '<PERSON>', 57, 'Kenya', 'DevOps Engineer', 'Monitoring, CI/CD, Containerization, Cloud Computing', 'PHD', 14, 'Spanish Intermediate')\n", "(143, '<PERSON>', 48, '<PERSON>', '<PERSON>', None, 'PHD', 3, 'French Fluent')\n", "(144, '<PERSON>', 48, 'Turks and Caicos Islands', 'Teacher', None, 'Commercial License', 1, 'Spanish Fluent')\n", "(145, '<PERSON>', 30, 'Central African Republic', 'Test Engineer', None, 'M Tech', 11, None)\n", "(146, '<PERSON> PhD', 48, 'Sierra Leone', 'Nurse', 'Surgery, Wound Care, Pediatrics', 'Diploma', 7, None)\n", "(147, '<PERSON>', 26, '<PERSON>', 'Test Engineer', None, 'Diploma', 14, 'Chinese Fluent')\n", "(148, '<PERSON>', 32, 'Zimbabwe', 'AI Engineer', None, 'BSc Nursing', 15, 'English Basic')\n", "(149, '<PERSON>', 23, '<PERSON>', '<PERSON><PERSON>', None, None, 14, 'Chinese Fluent')\n", "(150, '<PERSON>', 30, '<PERSON>', 'Cricketer', None, 'Diploma', 8, 'English Fluent')\n", "(151, '<PERSON>', 35, 'Oman', 'Network Administrator', 'Network Design, Network Security', 'Diploma', 2, 'Chinese Intermediate')\n", "(152, '<PERSON> Velez', 46, 'Isle of Man', 'Network Administrator', 'Network Design', 'Commercial License', 18, 'German B1')\n", "(153, '<PERSON>', 56, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, 'M Tech', 20, 'French Intermediate')\n", "(154, '<PERSON>', 23, 'Gibraltar', 'Data Scientist', 'Data Visualization, Machine Learning, Data Analysis, Statistics', 'Associate Degree', 8, None)\n", "(155, '<PERSON>', 58, 'Saudi Arabia', 'Data Scientist', 'Data Analysis', 'Certificate Program', 19, 'Spanish Basic')\n", "(156, '<PERSON>', 45, 'Saint Lucia', 'Data Scientist', 'Data Visualization, Data Analysis, Machine Learning, Statistics', 'M Tech', 20, 'Spanish Fluent')\n", "(157, '<PERSON> DD<PERSON>', 26, 'Romania', 'Engineer', 'Civil Engineering', 'Commercial License', 16, 'German C1')\n", "(158, '<PERSON>', 55, '<PERSON><PERSON><PERSON>', '<PERSON>', None, 'Certificate Program', 15, 'German B2')\n", "(159, '<PERSON>', 60, 'Gambia', 'Database Administrator', 'Backup and Recovery', 'PHD', 19, 'Chinese Fluent')\n", "(160, '<PERSON><PERSON>', 30, '<PERSON><PERSON><PERSON>', '<PERSON>', None, 'P<PERSON>', 8, 'German A1')\n", "(161, '<PERSON>', 45, 'Namibia', '<PERSON>', None, None, 14, 'French Fluent')\n", "(162, '<PERSON>', 28, 'Central African Republic', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 9, 'Chinese Intermediate')\n", "(163, '<PERSON>', 52, 'Antarctica (the territory South of 60 deg S)', 'Office Driver', None, 'BSc Nursing', 3, 'English Intermediate')\n", "(164, '<PERSON>', 28, 'Cameroon', 'Teacher', None, None, 18, 'French Fluent')\n", "(165, '<PERSON> PhD', 25, 'Lebanon', 'AI Engineer', 'Natural Language Processing', None, 11, 'Chinese Basic')\n", "(166, '<PERSON>', 53, '<PERSON><PERSON>', 'Movie Producer', None, 'High School', 2, 'French Intermediate')\n", "(167, '<PERSON>', 47, 'Palestinian Territory', 'UX/UI Designer', 'Usability Testing, User Research, Prototyping', 'Diploma', 10, 'German C2')\n", "(168, '<PERSON>', 22, 'Bahamas', '<PERSON><PERSON><PERSON>', None, 'Associate Degree', 2, None)\n", "(169, '<PERSON>', 28, '<PERSON>', '<PERSON><PERSON>ps Engineer', None, 'M <PERSON>', 3, None)\n", "(170, '<PERSON>', 38, 'Suriname', 'Database Administrator', None, 'PHD', 15, 'French Basic')\n", "(171, '<PERSON>', 53, 'Nicaragua', '<PERSON><PERSON>', <PERSON>, \"Master's Degree\", 14, 'German B2')\n", "(172, '<PERSON>', 38, 'Isle of Man', '<PERSON><PERSON>', <PERSON>, \"Master's Degree\", 16, 'Spanish Basic')\n", "(173, '<PERSON><PERSON>', 36, '<PERSON>', 'Test Engineer', None, 'Certificate Program', 13, 'German C1')\n", "(174, '<PERSON>', 37, 'South Georgia and the South Sandwich Islands', 'Teacher', None, 'PHD', 8, 'Chinese Intermediate')\n", "(175, '<PERSON>', 30, 'Bouvet Island (Bouvetoya)', 'Data Scientist', 'Data Analysis, Statistics', None, 1, None)\n", "(176, '<PERSON>', 52, 'Tajikistan', 'Nurse', 'Emergency', None, 19, 'Spanish Fluent')\n", "(177, '<PERSON>', 56, 'Cyprus', 'Cricketer', None, 'Commercial License', 15, 'English Basic')\n", "(178, 'Mr. <PERSON>', 34, '<PERSON><PERSON>', 'Teacher', None, \"Master's Degree\", 2, 'Chinese Basic')\n", "(179, '<PERSON>', 25, 'Sweden', 'Test Engineer', None, 'Certificate Program', 15, None)\n", "(180, '<PERSON>', 53, 'Albania', '<PERSON>', None, None, 15, 'Chinese Intermediate')\n", "(181, 'Mark Hall', 49, 'Holy See (Vatican City State)', 'Cybersecurity Specialist', None, 'High School', 16, 'Chinese Intermediate')\n", "(182, '<PERSON>', 41, 'United Arab Emirates', '<PERSON>', None, 'Certificate Program', 20, 'French Fluent')\n", "(183, '<PERSON><PERSON>', 36, 'Heard Island and McDonald Islands', 'Software Developer', 'Agile Development, Python, Java, JavaScript', \"Master's Degree\", 11, 'Chinese Intermediate')\n", "(184, 'Mr. <PERSON>', 47, '<PERSON>', 'Engineer', 'Civil Engineering', 'M Tech', 14, None)\n", "(185, '<PERSON>', 30, 'Taiwan', 'Movie Producer', None, 'B Tech', 19, 'English Basic')\n", "(186, '<PERSON>', 49, 'Spain', '<PERSON>', None, 'Commercial License', 6, 'English Basic')\n", "(187, '<PERSON>', 39, 'Hong Kong', 'Engineer', 'Chemical Engineering, Civil Engineering, Electrical Engineering, Mechanical Engineering', \"Bachelor's Degree\", 5, 'French Basic')\n", "(188, '<PERSON>', 52, 'Spain', '<PERSON>', None, 'PHD', 11, 'Chinese Basic')\n", "(189, '<PERSON>', 26, 'Ecuador', 'Social Media Influencer', <PERSON>, \"Bachelor's Degree\", 14, 'German A1')\n", "(190, '<PERSON>', 30, 'Greenland', 'Engineer', 'Electrical Engineering', 'Diploma', 11, 'German A2')\n", "(191, '<PERSON>', 35, 'Kenya', '<PERSON>', None, 'B Tech', 4, 'German B1')\n", "(192, '<PERSON>', 27, \"<PERSON><PERSON> d'I<PERSON>\", '<PERSON>', None, '<PERSON> <PERSON>', 11, None)\n", "(193, '<PERSON>', 31, '<PERSON>', '<PERSON><PERSON>', <PERSON>, 'BSc Nursing', 19, 'Spanish Basic')\n", "(194, '<PERSON>', 54, 'Cayman Islands', '<PERSON>', None, None, 11, None)\n", "(195, '<PERSON>', 28, 'Western Sahara', 'Nurse', 'ICU, Surgery, Wound Care, Emergency, Long-Term Care, Pediatrics', 'Associate Degree', 20, 'Chinese Fluent')\n", "(196, '<PERSON>', 59, 'Switzerland', 'School Bus Driver', 'Passenger Endorsement', 'BSc Nursing', 18, 'Spanish Basic')\n", "(197, '<PERSON>', 39, 'Liechtenstein', 'Pilot', None, None, 10, 'English Basic')\n", "(198, '<PERSON>', 45, 'Western Sahara', '<PERSON><PERSON>', None, None, 16, None)\n", "(199, '<PERSON>', 35, 'Aruba', 'Software Developer', 'Python, C++', 'Certificate Program', 6, 'German A1')\n", "(200, '<PERSON>', 40, '<PERSON><PERSON>', 'Teacher', None, \"Master's Degree\", 11, 'Spanish Intermediate')\n", "(201, 'Dr. <PERSON>', 37, '<PERSON><PERSON><PERSON><PERSON>', 'Licensed Practical Nurse', None, \"Master's Degree\", 7, 'English Basic')\n", "(202, '<PERSON>', 33, 'Trinidad and Tobago', 'Network Administrator', None, 'B Tech', 13, 'Chinese Fluent')\n", "(203, '<PERSON><PERSON><PERSON>', 25, 'Cook Islands', 'Movie Producer', None, None, 2, 'German A1')\n", "(204, '<PERSON>', 35, '<PERSON>', '<PERSON>', <PERSON>, \"Bachelor's Degree\", 8, 'Spanish Fluent')\n", "(205, '<PERSON>', 26, '<PERSON><PERSON>', 'DevOps Engineer', 'Containerization, Monitoring, CI/CD, Cloud Computing', 'M Tech', 11, 'Spanish Intermediate')\n", "(206, '<PERSON>', 47, '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', <PERSON>, 'M <PERSON>', 8, 'English Basic')\n", "(207, '<PERSON>', 22, '<PERSON> and the Grenadines', '<PERSON>', None, 'B Tech', 18, 'French Intermediate')\n", "(208, '<PERSON>', 54, '<PERSON>', 'Test Engineer', None, 'B Tech', 16, 'English Basic')\n", "(209, '<PERSON>', 45, 'Equatorial Guinea', 'UX/UI Designer', None, 'PHD', 15, 'English Basic')\n", "(210, '<PERSON>', 26, 'Burkina Faso', 'Database Administrator', 'Backup and Recovery, Performance Tuning, Database Design', 'Commercial License', 12, 'Spanish Intermediate')\n", "(211, '<PERSON>', 35, 'Romania', 'Truck Driver', None, 'Certificate Program', 17, 'Chinese Intermediate')\n", "(212, '<PERSON>', 59, 'Tanzania', 'Nurse', 'Pediatrics, Long-Term Care, Surgery, ICU', \"Bachelor's Degree\", 15, 'English Fluent')\n", "(213, '<PERSON>', 55, 'Cyprus', 'Social Media Influencer', <PERSON>, \"Bachelor's Degree\", 7, 'Chinese Fluent')\n", "(214, '<PERSON>', 60, 'Ethiopia', 'School Bus Driver', 'Passenger Endorsement', 'High School', 20, 'Spanish Basic')\n", "(215, '<PERSON>', 45, '<PERSON>', '<PERSON>', <PERSON>, 'M <PERSON>', 12, 'Spanish Intermediate')\n", "(216, '<PERSON>', 28, \"Lao People's Democratic Republic\", '<PERSON>', <PERSON>, \"Master's Degree\", 2, 'German B2')\n", "(217, '<PERSON>', 56, '<PERSON><PERSON>', '<PERSON>', <PERSON>, \"Bachelor's Degree\", 7, 'Spanish Fluent')\n", "(218, '<PERSON>', 39, '<PERSON><PERSON>bo<PERSON>', 'Test Engineer', None, None, 16, 'French Basic')\n", "(219, '<PERSON>', 54, 'Bolivia', 'Office Driver', <PERSON>, 'M Tech', 20, 'English Intermediate')\n", "(220, 'Mrs. <PERSON>', 36, 'Solomon Islands', 'Test Engineer', None, 'Commercial License', 15, 'German B1')\n", "(221, '<PERSON>', 36, 'Tunisia', '<PERSON>', None, None, 15, 'French Basic')\n", "(222, '<PERSON>', 55, 'Turkey', '<PERSON><PERSON> Trainer', None, 'Commercial License', 20, 'Chinese Intermediate')\n", "(223, '<PERSON>', 24, '<PERSON> <PERSON><PERSON>', 'Movie Director', None, 'Diploma', 3, None)\n", "(224, '<PERSON>', 39, 'Angola', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'Diploma', 13, None)\n", "(225, '<PERSON>', 40, 'Monaco', '<PERSON><PERSON><PERSON>', None, None, 1, 'Spanish Intermediate')\n", "(226, '<PERSON>', 32, 'Norfolk Island', '<PERSON><PERSON>', None, None, 11, 'Chinese Fluent')\n", "(227, '<PERSON>', 54, 'Estonia', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Certificate Program', 12, 'Spanish Intermediate')\n", "(228, '<PERSON>', 43, '<PERSON> and N<PERSON>is', '<PERSON>', None, 'High School', 4, None)\n", "(229, '<PERSON><PERSON>', 46, 'United States of America', 'Social Media Influencer', None, \"Master's Degree\", 11, 'German A1')\n", "(230, '<PERSON>', 41, 'Sierra Leone', 'Data Scientist', 'Data Visualization, Statistics, Data Analysis', 'Commercial License', 9, 'German B2')\n", "(231, '<PERSON>', 57, 'Bosnia and Herzegovina', 'Engineer', None, 'High School', 12, 'German A2')\n", "(232, '<PERSON>', 50, 'Turkmenistan', '<PERSON><PERSON><PERSON>', None, \"Master's Degree\", 9, 'Spanish Fluent')\n", "(233, '<PERSON>', 44, 'Turks and Caicos Islands', 'School Bus Driver', None, 'PHD', 5, 'French Intermediate')\n", "(234, '<PERSON>', 26, 'Namibia', 'Licensed Practical Nurse', None, 'B Tech', 7, None)\n", "(235, '<PERSON>', 35, 'Norway', 'Professor', <PERSON>, 'High School', 9, 'English Basic')\n", "(236, '<PERSON>', 59, 'Poland', 'Engineer', None, 'Certificate Program', 5, 'Spanish Fluent')\n", "(237, '<PERSON><PERSON><PERSON>', 37, '<PERSON><PERSON><PERSON>', 'DevOps Engineer', 'Monitoring', 'Certificate Program', 15, 'English Intermediate')\n", "(238, '<PERSON>', 54, '<PERSON>', 'Engineer', None, 'M Tech', 1, 'German C1')\n", "(239, '<PERSON>', 29, '<PERSON><PERSON> (Keeling) Islands', 'Software Developer', 'Agile Development, Java, Python', \"Bachelor's Degree\", 16, 'German B2')\n", "(240, '<PERSON>', 49, 'El Salvador', 'UX/UI Designer', 'Wireframing, User Research', 'PHD', 16, 'English Basic')\n", "(241, '<PERSON>', 23, 'Grenada', '<PERSON>', None, None, 12, 'Chinese Basic')\n", "(242, 'Mrs. <PERSON><PERSON>', 42, 'Angola', 'DevOps Engineer', <PERSON>, 'High School', 5, 'Chinese Fluent')\n", "(243, '<PERSON>', 33, 'Samoa', 'UX/UI Designer', 'User Research, Prototyping', None, 8, 'Chinese Fluent')\n", "(244, '<PERSON>', 39, 'Burkina Faso', 'School Bus Driver', None, 'Certificate Program', 4, 'Spanish Basic')\n", "(245, '<PERSON>', 57, 'Oman', 'Cybersecurity Specialist', 'Threat Analysis', 'BSc Nursing', 13, 'English Fluent')\n", "(246, '<PERSON>', 30, 'Haiti', 'Teacher', None, 'Diploma', 14, 'German A2')\n", "(247, '<PERSON>', 50, 'Costa Rica', '<PERSON>', None, 'B Tech', 11, 'Chinese Intermediate')\n", "(248, 'E<PERSON><PERSON> Lopez', 42, 'Italy', '<PERSON><PERSON>', None, None, 15, 'Chinese Fluent')\n", "(249, '<PERSON>', 43, 'Honduras', 'AI Engineer', None, \"Bachelor's Degree\", 9, None)\n", "(250, '<PERSON>', 34, 'Suriname', 'Test Engineer', None, 'PHD', 19, 'German B1')\n", "(251, '<PERSON>', 39, '<PERSON><PERSON>', 'Engineer', 'Civil Engineering', 'B Tech', 13, None)\n", "(252, '<PERSON>', 52, 'Grenada', 'Truck Driver', '<PERSON><PERSON><PERSON>, <PERSON>er, Defensive Driving, OTR', 'Diploma', 8, 'Chinese Basic')\n", "(253, '<PERSON>', 42, '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', None, 'High School', 12, 'Spanish Fluent')\n", "(254, '<PERSON>', 38, 'Denmark', 'Data Scientist', 'Statistics, Data Visualization', 'Commercial License', 11, None)\n", "(255, '<PERSON>', 54, 'Morocco', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'Certificate Program', 18, None)\n", "(256, '<PERSON>', 54, '<PERSON>', 'Truck Driver', 'OTR, Defensive Driving, Hazmat, Tanker', 'Certificate Program', 20, 'English Basic')\n", "(257, '<PERSON>', 37, 'Israel', 'Data Scientist', None, 'Certificate Program', 2, None)\n", "(258, '<PERSON>', 51, 'Guadeloupe', 'Database Administrator', None, 'High School', 11, 'Chinese Fluent')\n", "(259, '<PERSON>', 52, '<PERSON>', 'Database Administrator', 'Database Design', 'PHD', 20, 'Spanish Basic')\n", "(260, '<PERSON>', 41, 'Zambia', 'Network Administrator', 'Network Design', None, 9, 'English Fluent')\n", "(261, '<PERSON>', 59, 'Norway', '<PERSON>', None, 'Diploma', 11, 'Spanish Intermediate')\n", "(262, '<PERSON>', 32, 'Belize', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', \"Bachelor's Degree\", 7, 'Chinese Basic')\n", "(263, '<PERSON>', 29, 'Ireland', 'Engineer', None, 'P<PERSON>', 7, 'German C2')\n", "(264, '<PERSON>', 29, 'Western Sahara', '<PERSON><PERSON><PERSON>', None, \"Master's Degree\", 4, 'English Fluent')\n", "(265, '<PERSON>', 28, 'Saudi Arabia', 'Cybersecurity Specialist', 'Threat Analysis', None, 11, None)\n", "(266, '<PERSON>', 46, 'Latvia', 'Cybersecurity Specialist', 'Network Security, Threat Analysis, Compliance', 'Associate Degree', 3, 'English Intermediate')\n", "(267, '<PERSON>', 56, 'Equatorial Guinea', 'Office Driver', None, \"Bachelor's Degree\", 19, 'French Intermediate')\n", "(268, '<PERSON>', 34, '<PERSON> Pierre and Mi<PERSON>on', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis', 'Commercial License', 2, 'German A1')\n", "(269, '<PERSON>', 32, 'Somalia', 'Product Manager', None, 'BSc Nursing', 2, None)\n", "(270, '<PERSON>', 56, '<PERSON>', 'Truck Driver', None, 'Certificate Program', 16, None)\n", "(271, '<PERSON>', 41, 'Philippines', '<PERSON><PERSON> Trainer', <PERSON>, 'Diploma', 10, 'German C2')\n", "(272, '<PERSON>', 51, 'Bulgaria', 'Office Driver', None, 'Certificate Program', 12, None)\n", "(273, '<PERSON>', 28, 'Algeria', 'Nurse', 'Long-Term Care, Pediatrics, ICU', 'Associate Degree', 6, 'English Basic')\n", "(274, '<PERSON>', 26, 'Norfolk Island', 'AI Engineer', 'Computer Vision, Deep Learning, Robotics', None, 14, None)\n", "(275, 'Mr. <PERSON>', 26, 'Suriname', 'Truck Driver', 'Defensive Driving, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, O<PERSON>', 'Associate Degree', 17, None)\n", "(276, '<PERSON>', 53, 'Switzerland', 'Cybersecurity Specialist', 'Threat Analysis, Incident Response, Compliance, Network Security', None, 20, 'German B2')\n", "(277, '<PERSON>', 47, 'Netherlands', 'DevOps Engineer', 'CI/CD, Containerization', \"Master's Degree\", 3, 'German C1')\n", "(278, '<PERSON>', 50, 'Tajikistan', 'Cybersecurity Specialist', 'Compliance, Network Security', None, 10, 'Chinese Fluent')\n", "(279, '<PERSON>', 29, 'Seychelles', 'Software Developer', None, None, 20, None)\n", "(280, '<PERSON>', 60, '<PERSON>', 'Cricketer', None, 'Associate Degree', 12, None)\n", "(281, '<PERSON>', 49, 'Costa Rica', 'Test Engineer', None, None, 12, None)\n", "(282, '<PERSON>', 44, '<PERSON>', '<PERSON>', <PERSON>, 'BSc Nursing', 10, 'English Intermediate')\n", "(283, '<PERSON>', 55, 'Costa Rica', 'Movie Producer', None, 'Certificate Program', 1, 'Spanish Basic')\n", "(284, '<PERSON>', 46, 'Aruba', 'Teacher', None, \"Master's Degree\", 18, 'English Fluent')\n", "(285, '<PERSON>', 60, 'Brunei Darussalam', 'Movie Producer', None, 'High School', 2, 'Spanish Intermediate')\n", "(286, '<PERSON>', 32, 'Isle of Man', 'Software Developer', 'Python, Java, JavaScript, Agile Development, C++', 'B Tech', 20, 'Chinese Fluent')\n", "(287, '<PERSON>', 44, '<PERSON><PERSON><PERSON>', 'DevOps Engineer', 'Containerization, CI/CD', 'BSc Nursing', 7, None)\n", "(288, '<PERSON>', 26, 'Sri Lanka', 'Cybersecurity Specialist', 'Incident Response, Threat Analysis, Network Security', 'B Tech', 15, 'German C2')\n", "(289, '<PERSON>', 56, 'Guam', 'Social Media Influencer', None, 'Certificate Program', 15, 'Chinese Fluent')\n", "(290, '<PERSON>', 53, '<PERSON><PERSON>', '<PERSON>', <PERSON>, 'High School', 17, 'Chinese Fluent')\n", "(291, '<PERSON>', 22, 'Sierra Leone', '<PERSON><PERSON> Trainer', None, 'Certificate Program', 8, None)\n", "(292, '<PERSON>', 46, 'Swaziland', 'Social Media Influencer', None, \"Bachelor's Degree\", 16, 'French Intermediate')\n", "(293, '<PERSON>', 29, 'Maldives', 'UX/UI Designer', 'User Research, Usability Testing, Prototyping', \"Bachelor's Degree\", 13, 'English Basic')\n", "(294, '<PERSON>', 41, 'Sri Lanka', 'Office Driver', <PERSON>, \"Master's Degree\", 6, 'Spanish Fluent')\n", "(295, '<PERSON>', 60, '<PERSON><PERSON>', '<PERSON><PERSON>er', None, 'P<PERSON>', 8, 'French Intermediate')\n", "(296, '<PERSON>', 38, 'Antigua and Barbuda', '<PERSON><PERSON>', <PERSON>, 'M Tech', 18, 'French Basic')\n", "(297, '<PERSON>', 60, 'Croatia', '<PERSON>', <PERSON>, \"Bachelor's Degree\", 5, 'English Basic')\n", "(298, '<PERSON>', 43, 'Pakistan', 'Professor', None, None, 12, None)\n", "(299, '<PERSON>', 23, 'Kuwait', 'Professor', None, None, 18, 'Chinese Fluent')\n", "(300, '<PERSON><PERSON>', 45, 'Sierra Leone', 'Movie Producer', None, 'Certificate Program', 3, 'French Basic')\n", "(301, '<PERSON>', 46, 'Tunisia', 'Cybersecurity Specialist', 'Network Security, Compliance, Incident Response, Threat Analysis', 'Associate Degree', 14, 'English Fluent')\n", "(302, '<PERSON>', 25, 'Congo', '<PERSON>', None, 'Diploma', 20, 'French Fluent')\n", "(303, '<PERSON>', 58, 'Barbados', 'Data Scientist', 'Data Analysis, Data Visualization, Statistics, Machine Learning', 'B Tech', 9, None)\n", "(304, '<PERSON>', 27, 'Cayman Islands', 'Office Driver', None, 'High School', 1, 'English Intermediate')\n", "(305, '<PERSON>', 22, 'Christmas Island', 'Movie Director', None, 'Associate Degree', 7, 'Spanish Intermediate')\n", "(306, '<PERSON>', 22, 'Greece', 'AI Engineer', None, 'PHD', 5, 'English Basic')\n", "(307, '<PERSON>', 37, 'Cook Islands', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'High School', 19, 'Spanish Fluent')\n", "(308, '<PERSON>', 39, 'Holy See (Vatican City State)', 'Test Engineer', None, 'Associate Degree', 9, 'Spanish Fluent')\n", "(309, '<PERSON>', 39, 'Sri Lanka', 'Database Administrator', None, 'M Tech', 17, 'English Intermediate')\n", "(310, '<PERSON>', 22, 'Russian Federation', 'Test Engineer', None, 'Certificate Program', 7, None)\n", "(311, '<PERSON>', 56, '<PERSON>', 'Professor', <PERSON>, 'Associate Degree', 3, 'German B1')\n", "(312, '<PERSON>', 34, 'Central African Republic', 'Cybersecurity Specialist', None, 'M Tech', 4, None)\n", "(313, '<PERSON>', 29, 'Slovakia (Slovak Republic)', 'UX/UI Designer', 'User Research, Wireframing, Usability Testing', None, 18, None)\n", "(314, '<PERSON>', 26, 'Puerto Rico', '<PERSON>', <PERSON>, 'M Tech', 2, 'French Intermediate')\n", "(315, '<PERSON>', 43, 'Taiwan', 'Test Engineer', None, 'B Tech', 7, 'Spanish Fluent')\n", "(316, '<PERSON>', 22, '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', None, 'Certificate Program', 7, 'Chinese Basic')\n", "(317, '<PERSON>', 28, '<PERSON>', '<PERSON>er', <PERSON>, 'Associate Degree', 17, 'English Intermediate')\n", "(318, '<PERSON>', 29, 'Vanuatu', 'UX/UI Designer', None, \"Bachelor's Degree\", 19, 'German A2')\n", "(319, '<PERSON>', 44, '<PERSON>', '<PERSON>', None, 'B Tech', 12, None)\n", "(320, '<PERSON>', 36, '<PERSON><PERSON>', 'Database Administrator', <PERSON>, 'B Tech', 12, 'German B2')\n", "(321, '<PERSON>', 52, 'China', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', None, 9, 'Chinese Basic')\n", "(322, '<PERSON>', 27, 'Falkland Islands (Malvinas)', 'Dev<PERSON>ps Engineer', None, 'High School', 3, None)\n", "(323, '<PERSON>', 27, '<PERSON>', 'Truck Driver', '<PERSON><PERSON>, <PERSON><PERSON><PERSON>, OTR', None, 9, 'Spanish Basic')\n", "(324, '<PERSON>', 38, 'Nigeria', 'School Bus Driver', None, 'BSc Nursing', 6, 'Chinese Basic')\n", "(325, '<PERSON>', 30, 'Antigua and Barbuda', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'B Tech', 15, None)\n", "(326, '<PERSON>', 34, 'French Southern Territories', 'Engineer', 'Mechanical Engineering, Civil Engineering, Electrical Engineering', 'M Tech', 2, None)\n", "(327, '<PERSON>', 30, 'Spain', 'Data Scientist', 'Statistics, Data Visualization, Machine Learning', None, 9, 'Spanish Intermediate')\n", "(328, '<PERSON>', 22, 'Indonesia', 'Test Engineer', None, None, 12, None)\n", "(329, '<PERSON>', 23, '<PERSON><PERSON><PERSON>', 'Database Administrator', 'Database Design, Backup and Recovery', \"Bachelor's Degree\", 5, 'Spanish Intermediate')\n", "(330, '<PERSON>', 35, 'Germany', 'Truck Driver', 'Defensive Driving, Hazmat, Tanker', 'Certificate Program', 17, 'English Intermediate')\n", "(331, '<PERSON>', 60, 'Andorra', 'Cybersecurity Specialist', 'Compliance, Threat Analysis', 'Associate Degree', 14, 'German A1')\n", "(332, '<PERSON>', 30, '<PERSON>', 'DevOps Engineer', 'Monitoring, CI/CD', 'Commercial License', 8, 'German A1')\n", "(333, '<PERSON>', 58, '<PERSON>', 'Database Administrator', 'Backup and Recovery, Database Design', 'PHD', 11, 'French Basic')\n", "(334, '<PERSON>', 39, 'Brunei Darussalam', 'Pilot', None, 'High School', 10, 'Spanish Basic')\n", "(335, '<PERSON>', 39, 'Burkina Faso', '<PERSON>', <PERSON>, 'M Tech', 11, 'Chinese Basic')\n", "(336, '<PERSON>', 51, '<PERSON>', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', None, 1, 'Chinese Basic')\n", "(337, '<PERSON>', 24, 'Tajikistan', 'Office Driver', None, 'Commercial License', 20, 'Spanish Basic')\n", "(338, '<PERSON>', 31, 'French Southern Territories', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'M Tech', 20, 'German B2')\n", "(339, '<PERSON>', 33, '<PERSON><PERSON><PERSON>', '<PERSON>', None, 'Associate Degree', 9, 'German C1')\n", "(340, '<PERSON>', 39, 'Russian Federation', 'Office Driver', None, None, 7, 'Chinese Basic')\n", "(341, '<PERSON>', 26, 'American Samoa', 'DevOps Engineer', 'Cloud Computing', 'Diploma', 20, 'Chinese Fluent')\n", "(342, '<PERSON>', 51, '<PERSON><PERSON>', 'Network Administrator', 'Network Design, Network Security', 'High School', 1, 'Chinese Fluent')\n", "(343, '<PERSON>', 51, '<PERSON>', '<PERSON>', None, \"Bachelor's Degree\", 13, 'German B1')\n", "(344, '<PERSON>', 23, 'Ireland', '<PERSON>', None, None, 17, None)\n", "(345, '<PERSON>', 30, 'Chile', 'DevOps Engineer', None, None, 12, 'Spanish Intermediate')\n", "(346, '<PERSON>', 40, '<PERSON><PERSON>', 'Software Developer', None, \"Bachelor's Degree\", 12, 'English Fluent')\n", "(347, '<PERSON>', 57, \"Lao People's Democratic Republic\", 'UX/UI Designer', 'User Research, Wireframing', None, 17, 'French Basic')\n", "(348, '<PERSON>', 31, '<PERSON><PERSON>', '<PERSON><PERSON>er', <PERSON>, 'BSc Nursing', 8, 'Spanish Intermediate')\n", "(349, '<PERSON><PERSON><PERSON>', 35, '<PERSON><PERSON><PERSON><PERSON>', 'Cybersecurity Specialist', None, \"Master's Degree\", 16, None)\n", "(350, '<PERSON>', 32, 'Ethiopia', 'Office Driver', None, \"Bachelor's Degree\", 4, 'Spanish Intermediate')\n", "(351, '<PERSON>', 33, '<PERSON>', 'Office Driver', None, None, 6, None)\n", "(352, '<PERSON>', 29, 'Namibia', 'Database Administrator', 'Database Design, Performance Tuning, Backup and Recovery', 'High School', 16, 'English Fluent')\n", "(353, '<PERSON><PERSON>', 38, 'Brazil', 'Software Developer', 'Python, C++, Java, Agile Development, JavaScript', 'High School', 11, 'German C1')\n", "(354, '<PERSON><PERSON>', 23, 'Western Sahara', 'Test Engineer', None, \"Master's Degree\", 6, 'French Fluent')\n", "(355, '<PERSON>', 58, 'Honduras', 'Software Developer', None, 'M Tech', 14, 'Chinese Basic')\n", "(356, '<PERSON>', 34, 'Turks and Caicos Islands', 'Pilot', None, 'Certificate Program', 16, 'French Intermediate')\n", "(357, '<PERSON>', 40, '<PERSON><PERSON>', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'BSc Nursing', 20, 'English Basic')\n", "(358, '<PERSON>', 35, 'Belarus', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 12, 'German C1')\n", "(359, '<PERSON>', 39, 'Nigeria', '<PERSON>', None, None, 9, 'Chinese Basic')\n", "(360, '<PERSON>', 58, 'Russian Federation', 'Office Driver', None, 'Diploma', 12, 'English Intermediate')\n", "(361, '<PERSON>', 42, '<PERSON><PERSON>', 'Engineer', None, 'M Tech', 7, 'Spanish Fluent')\n", "(362, '<PERSON>', 42, '<PERSON><PERSON>', 'Data Scientist', 'Statistics, Data Visualization', 'BSc Nursing', 6, None)\n", "(363, '<PERSON>', 26, '<PERSON>', '<PERSON><PERSON><PERSON>', None, 'P<PERSON>', 11, 'Spanish Intermediate')\n", "(364, '<PERSON>', 31, 'Slovakia (Slovak Republic)', '<PERSON>', None, None, 12, 'French Fluent')\n", "(365, '<PERSON>', 54, 'Nigeria', 'Test Engineer', None, 'High School', 15, 'Chinese Fluent')\n", "(366, '<PERSON>', 60, 'Ukraine', 'Engineer', None, 'Certificate Program', 8, 'Chinese Fluent')\n", "(367, '<PERSON>', 28, 'Seychelles', 'Truck Driver', '<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Defensive Driving', 'M Tech', 13, 'Spanish Basic')\n", "(368, '<PERSON>', 52, '<PERSON><PERSON>', 'Product Manager', None, 'B Tech', 6, None)\n", "(369, '<PERSON>', 52, 'Kenya', 'Software Developer', None, 'P<PERSON>', 20, None)\n", "(370, '<PERSON>', 50, 'Antarctica (the territory South of 60 deg S)', 'Cricketer', None, None, 1, 'English Fluent')\n", "(371, '<PERSON>', 44, 'New Zealand', 'Test Engineer', None, 'B Tech', 8, 'Spanish Basic')\n", "(372, '<PERSON>', 48, '<PERSON>', '<PERSON>', None, 'M Tech', 18, 'Spanish Intermediate')\n", "(373, '<PERSON>', 41, 'Cayman Islands', '<PERSON><PERSON><PERSON>', None, 'High School', 13, 'German A2')\n", "(374, '<PERSON>', 27, 'Liberia', 'AI Engineer', None, 'PHD', 10, 'Chinese Basic')\n", "(375, '<PERSON>', 58, '<PERSON><PERSON><PERSON><PERSON>', 'Network Administrator', None, 'BSc Nursing', 18, 'Chinese Intermediate')\n", "(376, 'Mrs. <PERSON>', 47, 'Azerbaijan', 'Network Administrator', None, 'High School', 19, 'German C2')\n", "(377, '<PERSON>', 53, 'South Africa', 'DevOps Engineer', None, 'Commercial License', 2, 'English Intermediate')\n", "(378, 'Mrs. <PERSON>', 36, 'United States Minor Outlying Islands', 'Licensed Practical Nurse', None, \"Bachelor's Degree\", 5, None)\n", "(379, '<PERSON>', 53, 'Ukraine', 'Product Manager', 'Market Research, Project Management, Product Development', 'PHD', 8, None)\n", "(380, '<PERSON>', 50, 'Croatia', 'Database Administrator', 'Database Design, Performance Tuning', 'Diploma', 13, None)\n", "(381, '<PERSON>', 54, '<PERSON>', '<PERSON><PERSON><PERSON>', None, 'B Tech', 1, 'German C2')\n", "(382, '<PERSON>', 29, 'Ecuador', '<PERSON><PERSON><PERSON>', None, 'M <PERSON>', 8, 'German B2')\n", "(383, '<PERSON>', 45, 'Zambia', '<PERSON>', None, 'High School', 12, 'English Intermediate')\n", "(384, '<PERSON>', 44, '<PERSON><PERSON>', 'Database Administrator', None, None, 1, 'Chinese Fluent')\n", "(385, '<PERSON>', 33, 'Isle of Man', '<PERSON>', <PERSON>, 'M Tech', 20, 'French Fluent')\n", "(386, '<PERSON>', 40, '<PERSON>', 'Licensed Practical Nurse', None, 'PHD', 20, None)\n", "(387, '<PERSON>', 49, 'Syrian Arab Republic', 'Office Driver', None, None, 16, None)\n", "(388, '<PERSON>', 56, 'Antigua and Barbuda', 'Movie Director', None, \"Bachelor's Degree\", 2, 'French Basic')\n", "(389, '<PERSON>', 35, '<PERSON><PERSON> (Keeling) Islands', 'Data Scientist', 'Data Visualization, Machine Learning, Statistics', None, 7, 'German C1')\n", "(390, '<PERSON>', 36, '<PERSON><PERSON><PERSON>', '<PERSON>', None, \"Bachelor's Degree\", 8, 'English Basic')\n", "(391, '<PERSON>', 53, '<PERSON>', '<PERSON>', None, 'Certificate Program', 8, None)\n", "(392, '<PERSON>', 24, '<PERSON>', '<PERSON><PERSON>', None, 'P<PERSON>', 20, 'Spanish Intermediate')\n", "(393, '<PERSON>', 33, 'Australia', 'Database Administrator', 'Backup and Recovery, Database Design, Performance Tuning', 'PHD', 12, None)\n", "(394, '<PERSON>', 59, 'Iraq', 'Social Media Influencer', None, 'B Tech', 13, 'French Basic')\n", "(395, '<PERSON>', 28, '<PERSON><PERSON>', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Diploma', 17, 'English Basic')\n", "(396, 'Kristin Park', 49, 'Oman', '<PERSON>', None, 'High School', 15, 'English Intermediate')\n", "(397, '<PERSON>', 51, 'Luxembourg', '<PERSON><PERSON><PERSON>', None, 'Certificate Program', 9, 'French Intermediate')\n", "(398, '<PERSON>', 24, '<PERSON><PERSON>', 'AI Engineer', 'Computer Vision, Robotics, Natural Language Processing, Deep Learning', 'Certificate Program', 16, 'German B1')\n", "(399, '<PERSON>', 44, 'Netherlands Antilles', 'Nurse', 'Pediatrics, Surgery', 'Associate Degree', 6, 'Chinese Intermediate')\n", "(400, '<PERSON>', 53, '<PERSON>', '<PERSON>', None, 'High School', 20, None)\n", "(401, '<PERSON>', 54, 'Taiwan', 'Engineer', 'Chemical Engineering, Civil Engineering', 'Commercial License', 19, None)\n", "(402, '<PERSON>', 25, 'Bosnia and Herzegovina', 'Test Engineer', None, None, 17, 'Spanish Basic')\n", "(403, '<PERSON>', 58, 'Sao Tome and Principe', 'Social Media Influencer', None, 'M Tech', 9, 'German B1')\n", "(404, '<PERSON>', 40, '<PERSON><PERSON>', 'School Bus Driver', 'Defensive Driving', 'Certificate Program', 12, 'Chinese Intermediate')\n", "(405, '<PERSON>', 25, 'British Virgin Islands', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'M Tech', 19, 'French Fluent')\n", "(406, '<PERSON>', 58, 'Slovakia (Slovak Republic)', 'Network Administrator', 'Network Security, Network Design', 'Diploma', 13, 'French Basic')\n", "(407, '<PERSON>', 46, 'North Macedonia', 'Test Engineer', None, 'Associate Degree', 11, 'Spanish Intermediate')\n", "(408, '<PERSON>', 39, '<PERSON>', '<PERSON><PERSON><PERSON>', None, '<PERSON><PERSON>', 4, None)\n", "(409, '<PERSON>', 47, 'Gabon', 'Social Media Influencer', None, 'B Tech', 6, 'German A2')\n", "(410, '<PERSON>', 37, 'Svalbard & Jan Mayen Islands', '<PERSON>', None, 'M Tech', 1, 'English Fluent')\n", "(411, '<PERSON>', 49, '<PERSON><PERSON>', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', 'Associate Degree', 7, 'German C1')\n", "(412, '<PERSON><PERSON>', 48, 'Ghana', 'Movie Director', None, 'Commercial License', 20, 'Spanish Intermediate')\n", "(413, '<PERSON>', 40, 'Luxembourg', 'Product Manager', 'Project Management, Product Development, Market Research', 'M Tech', 18, 'Spanish Basic')\n", "(414, '<PERSON>', 51, 'Congo', '<PERSON><PERSON><PERSON>', None, 'Commercial License', 16, 'German B2')\n", "(415, 'Brent Durham', 54, 'Burundi', 'AI Engineer', 'Natural Language Processing, Computer Vision', 'B Tech', 11, 'French Intermediate')\n", "(416, '<PERSON>', 26, 'Afghanistan', 'Test Engineer', None, 'Certificate Program', 8, None)\n", "(417, '<PERSON>', 51, 'French Guiana', 'Engineer', <PERSON>, 'M Tech', 5, 'Chinese Intermediate')\n", "(418, '<PERSON>', 39, 'Bahamas', 'Cybersecurity Specialist', None, 'PHD', 10, 'Chinese Basic')\n", "(419, '<PERSON>', 37, 'Liechtenstein', 'Product Manager', 'Product Development, Market Research', 'High School', 18, 'Spanish Fluent')\n", "(420, '<PERSON>', 60, 'Sri Lanka', 'Cybersecurity Specialist', None, 'High School', 6, None)\n", "(421, '<PERSON>', 41, '<PERSON>', '<PERSON>', None, None, 19, None)\n", "(422, '<PERSON>', 26, 'Russian Federation', '<PERSON><PERSON> Trainer', None, 'Commercial License', 14, 'English Fluent')\n", "(423, '<PERSON>', 58, 'Algeria', 'Office Driver', None, \"Bachelor's Degree\", 12, 'French Basic')\n", "(424, '<PERSON>', 40, 'Bahrain', 'Social Media Influencer', None, None, 20, None)\n", "(425, '<PERSON>', 47, '<PERSON>', 'Engineer', None, None, 12, 'Chinese Fluent')\n", "(426, '<PERSON>', 28, '<PERSON>', 'Professor', None, 'Diploma', 6, 'Spanish Intermediate')\n", "(427, '<PERSON>', 47, '<PERSON>', '<PERSON>', None, 'B <PERSON>', 9, None)\n", "(428, '<PERSON>', 32, '<PERSON>', 'Data Scientist', 'Data Visualization', 'PHD', 2, 'Spanish Fluent')\n", "(429, '<PERSON>', 57, '<PERSON> and Nevis', 'Truck Driver', '<PERSON><PERSON>, OTR', 'Diploma', 14, 'German C1')\n", "(430, '<PERSON>', 23, 'Somalia', 'Movie Producer', None, 'Associate Degree', 16, None)\n", "(431, '<PERSON>', 31, 'Fiji', 'Movie Director', None, 'PHD', 11, 'French Basic')\n", "(432, '<PERSON>', 36, 'Argentina', 'Teacher', None, 'Diploma', 15, 'French Intermediate')\n", "(433, '<PERSON>', 25, 'A<PERSON>', 'Product Manager', None, \"Master's Degree\", 5, None)\n", "(434, 'Tyler Hall', 31, 'Mal<PERSON>', 'Nurse', 'I<PERSON>, Long-Term Care, Wound Care, Emergency, Surgery', 'M Tech', 3, 'German C2')\n", "(435, '<PERSON>', 25, 'Marshall Islands', '<PERSON>', None, None, 19, None)\n", "(436, '<PERSON>', 39, '<PERSON><PERSON>', '<PERSON>', <PERSON>, 'Associate Degree', 20, 'French Basic')\n", "(437, '<PERSON>', 56, 'Iceland', 'UX/UI Designer', 'Usability Testing, Prototyping, User Research, Wireframing', None, 16, 'German B2')\n", "(438, '<PERSON>', 58, '<PERSON>', '<PERSON>', <PERSON>, 'Associate Degree', 18, 'English Fluent')\n", "(439, 'Mrs. <PERSON>', 54, 'American Samoa', 'Social Media Influencer', None, \"Master's Degree\", 20, 'English Fluent')\n", "(440, '<PERSON>', 24, '<PERSON><PERSON><PERSON>', 'Software Developer', 'JavaScript, C++, Agile Development, Python, Java', 'High School', 10, 'Chinese Fluent')\n", "(441, '<PERSON>', 51, '<PERSON>', '<PERSON><PERSON>', <PERSON>, 'M <PERSON>', 5, 'English Intermediate')\n", "(442, '<PERSON>', 44, 'Papua New Guinea', 'Office Driver', None, 'Commercial License', 7, 'English Basic')\n", "(443, '<PERSON>', 24, 'Hungary', 'Engineer', 'Mechanical Engineering, Civil Engineering, Chemical Engineering, Electrical Engineering', 'Associate Degree', 2, 'Chinese Intermediate')\n", "(444, '<PERSON>', 51, 'Northern Mariana Islands', 'Licensed Practical Nurse', 'Long-Term Care', None, 13, 'French Basic')\n", "(445, '<PERSON>', 47, 'Greenland', 'Movie Producer', <PERSON>, 'High School', 3, 'Chinese Fluent')\n", "(446, '<PERSON>', 23, 'Cyprus', 'Engineer', 'Chemical Engineering', \"Bachelor's Degree\", 14, None)\n", "(447, '<PERSON>', 52, '<PERSON>', 'Cybersecurity Specialist', 'Incident Response, Network Security, Compliance, Threat Analysis', 'M Tech', 9, None)\n", "(448, '<PERSON>', 28, '<PERSON><PERSON>', 'Software Developer', 'Python, JavaScript, Java, C++', None, 14, None)\n", "(449, '<PERSON>', 45, 'Sierra Leone', 'Movie Director', None, 'M Tech', 3, 'Spanish Intermediate')\n", "(450, '<PERSON>', 56, 'Liberia', 'Movie Director', None, 'BSc Nursing', 11, 'English Fluent')\n", "(451, '<PERSON>', 44, 'Papua New Guinea', 'Software Developer', 'C++, Agile Development', 'BSc Nursing', 15, 'English Basic')\n", "(452, '<PERSON>', 34, '<PERSON><PERSON>', 'Teacher', None, 'BSc Nursing', 11, 'French Fluent')\n", "(453, '<PERSON>', 58, 'French Guiana', 'AI Engineer', None, 'PHD', 6, 'German B2')\n", "(454, '<PERSON>', 51, '<PERSON>', 'Product Manager', 'Product Development, Project Management', \"Master's Degree\", 2, None)\n", "(455, 'Mr. <PERSON>', 36, 'Korea', 'Data Scientist', None, 'Diploma', 19, 'English Fluent')\n", "(456, '<PERSON>', 55, 'Solomon Islands', 'Teacher', <PERSON>, 'B Tech', 5, 'English Intermediate')\n", "(457, '<PERSON>', 48, 'Haiti', 'Network Administrator', 'Network Design', 'BSc Nursing', 6, 'French Fluent')\n", "(458, '<PERSON>', 51, 'Ghana', 'DevOps Engineer', None, \"Bachelor's Degree\", 1, 'French Basic')\n", "(459, '<PERSON>', 51, 'Tanzania', 'Product Manager', None, \"Bachelor's Degree\", 1, 'Chinese Basic')\n", "(460, '<PERSON>', 51, '<PERSON><PERSON><PERSON>', 'School Bus Driver', None, 'Commercial License', 3, None)\n", "(461, '<PERSON>', 28, '<PERSON>', '<PERSON><PERSON>er', None, 'P<PERSON>', 3, 'Chinese Fluent')\n", "(462, '<PERSON>', 50, '<PERSON><PERSON><PERSON>', 'Truck Driver', 'Defensive Driving, OTR, Hazmat', 'Diploma', 20, 'Spanish Fluent')\n", "(463, '<PERSON>', 57, 'New Zealand', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis, Network Security', 'B Tech', 5, 'French Basic')\n", "(464, '<PERSON>', 26, 'Guam', 'Network Administrator', 'Network Design', None, 1, 'Spanish Intermediate')\n", "(465, '<PERSON>', 41, 'South Africa', 'AI Engineer', None, None, 10, 'Spanish Fluent')\n", "(466, '<PERSON>', 49, 'Kuwait', 'Social Media Influencer', None, 'PHD', 16, None)\n", "(467, '<PERSON>', 50, 'Guyana', 'Professor', None, None, 20, None)\n", "(468, '<PERSON>', 29, 'Sri Lanka', '<PERSON><PERSON> Trainer', None, 'Commercial License', 11, 'Chinese Intermediate')\n", "(469, 'Miss <PERSON>', 44, 'Burkina Faso', 'Software Developer', 'JavaScript, Java', 'Commercial License', 13, 'Chinese Basic')\n", "(470, '<PERSON>', 29, 'Austria', 'Test Engineer', None, 'PHD', 1, 'French Basic')\n", "(471, '<PERSON>', 25, 'Mozambique', 'Network Administrator', 'Network Security, Network Design, Troubleshooting', None, 7, 'English Fluent')\n", "(472, '<PERSON>', 41, '<PERSON>', '<PERSON>', None, 'Diploma', 14, None)\n", "(473, '<PERSON>', 26, 'Brunei Darussalam', '<PERSON>', None, 'Diploma', 13, None)\n", "(474, '<PERSON><PERSON>', 45, 'Botswana', 'Truck Driver', 'Tanker, Defensive Driving, Hazmat', 'Certificate Program', 18, 'French Fluent')\n", "(475, '<PERSON>', 25, 'Vanuatu', '<PERSON><PERSON>er', None, 'Certificate Program', 6, 'English Fluent')\n", "(476, '<PERSON>', 41, 'Solomon Islands', '<PERSON>', None, 'B Tech', 18, 'Chinese Fluent')\n", "(477, '<PERSON>', 51, 'Bolivia', '<PERSON>', <PERSON>, \"Master's Degree\", 5, 'German C2')\n", "(478, '<PERSON>', 22, '<PERSON>', '<PERSON>', None, None, 4, 'German A2')\n", "(479, '<PERSON>', 46, 'Gambia', 'UX/UI Designer', 'Prototyping, Wireframing, User Research', 'B Tech', 12, 'German A1')\n", "(480, '<PERSON>', 48, 'British Indian Ocean Territory (Chagos Archipelago)', 'Office Driver', None, None, 20, 'German C1')\n", "(481, '<PERSON>', 27, 'Turkey', 'Database Administrator', 'Database Design, Performance Tuning', \"Bachelor's Degree\", 16, 'Spanish Intermediate')\n", "(482, '<PERSON><PERSON>', 44, '<PERSON>', '<PERSON>', <PERSON>, 'B Tech', 20, 'Chinese Intermediate')\n", "(483, '<PERSON>', 32, 'Qatar', 'Network Administrator', 'Network Design, Network Security', 'BSc Nursing', 4, 'Chinese Basic')\n", "(484, '<PERSON>', 45, '<PERSON>', '<PERSON><PERSON>', None, 'PHD', 1, 'English Fluent')\n", "(485, '<PERSON>', 24, 'Papua New Guinea', 'Truck Driver', 'OTR', None, 9, 'Chinese Fluent')\n", "(486, '<PERSON>', 43, 'Bulgaria', 'Teacher', None, 'B Tech', 20, 'French Basic')\n", "(487, '<PERSON>', 42, 'Croatia', 'Movie Director', None, \"Master's Degree\", 15, 'Chinese Basic')\n", "(488, '<PERSON>', 44, '<PERSON>', '<PERSON>', <PERSON>, 'P<PERSON>', 19, 'Spanish Intermediate')\n", "(489, '<PERSON>', 55, '<PERSON>', 'Truck Driver', '<PERSON><PERSON>, Hazmat', 'B <PERSON>', 17, 'French Fluent')\n", "(490, '<PERSON>', 51, 'India', '<PERSON>', None, \"Bachelor's Degree\", 19, None)\n", "(491, '<PERSON>', 44, 'Portugal', 'Data Scientist', 'Data Analysis', \"Master's Degree\", 6, 'French Intermediate')\n", "(492, '<PERSON>', 47, 'Bosnia and Herzegovina', '<PERSON>', <PERSON>, 'Associate Degree', 10, 'Chinese Fluent')\n", "(493, '<PERSON>', 47, 'Canada', 'Professor', None, 'Diploma', 16, 'French Intermediate')\n", "(494, '<PERSON>', 41, 'Kuwait', 'School Bus Driver', 'Passenger Endorsement', 'Commercial License', 11, 'Spanish Fluent')\n", "(495, '<PERSON>', 27, 'Russian Federation', '<PERSON>', None, None, 15, None)\n", "(496, '<PERSON>', 33, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', None, \"Master's Degree\", 17, None)\n", "(497, '<PERSON>', 46, 'Belize', 'Office Driver', None, None, 1, 'Chinese Basic')\n", "(498, '<PERSON>', 46, 'Switzerland', 'DevOps Engineer', 'CI/CD', 'BSc Nursing', 15, 'Spanish Basic')\n", "(499, '<PERSON>', 26, 'Albania', 'Database Administrator', None, None, 20, None)\n", "(500, '<PERSON>', 39, '<PERSON><PERSON><PERSON><PERSON>', 'Social Media Influencer', None, None, 10, 'English Basic')\n"]}], "source": ["for row in cursor.fetchall():\n", "    print(row)"]}, {"cell_type": "code", "execution_count": null, "id": "10ba0022", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "\n", "conn = sqlite3.connect(\"databases/candidates_details_500.db\")\n", "conn"]}, {"cell_type": "code", "execution_count": 5, "id": "59f0ac09", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>candidates</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>sqlite_sequence</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              name\n", "0       candidates\n", "1  sqlite_sequence"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# get the table names in the database\n", "df = pd.read_sql_query(\"SELECT name FROM sqlite_master WHERE type='table';\", conn)\n", "df"]}, {"cell_type": "code", "execution_count": 8, "id": "b77477a1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>age</th>\n", "      <th>country</th>\n", "      <th>profession</th>\n", "      <th>skills</th>\n", "      <th>education</th>\n", "      <th>experience_years</th>\n", "      <th>language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>47</td>\n", "      <td>Chad</td>\n", "      <td>AI Engineer</td>\n", "      <td>Robotics, Computer Vision, Natural Language Pr...</td>\n", "      <td>B Tech</td>\n", "      <td>5</td>\n", "      <td>French Basic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>56</td>\n", "      <td>Portugal</td>\n", "      <td>Office Driver</td>\n", "      <td>None</td>\n", "      <td>Certificate Program</td>\n", "      <td>9</td>\n", "      <td>French Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>43</td>\n", "      <td>Swaziland</td>\n", "      <td>Office Driver</td>\n", "      <td>None</td>\n", "      <td>B Tech</td>\n", "      <td>1</td>\n", "      <td>English Intermediate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>Tunisia</td>\n", "      <td>Software Developer</td>\n", "      <td>C++, Python, JavaScript</td>\n", "      <td>BSc Nursing</td>\n", "      <td>2</td>\n", "      <td>Chinese Fluent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>60</td>\n", "      <td>Tunisia</td>\n", "      <td>Network Administrator</td>\n", "      <td>Troubleshooting, Network Security, Network Design</td>\n", "      <td>High School</td>\n", "      <td>10</td>\n", "      <td>German B1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                   name  age    country             profession  \\\n", "0   1    <PERSON><PERSON><PERSON>   47       Chad            AI Engineer   \n", "1   2        <PERSON>   56   Portugal          Office Driver   \n", "2   3       <PERSON>   43  Swaziland          Office Driver   \n", "3   4  <PERSON>   25    Tunisia     Software Developer   \n", "4   5             <PERSON>   60    Tunisia  Network Administrator   \n", "\n", "                                              skills            education  \\\n", "0  Robotics, Computer Vision, Natural Language Pr...               B Tech   \n", "1                                               None  Certificate Program   \n", "2                                               None               B Tech   \n", "3                            C++, Python, JavaScript          BSc Nursing   \n", "4  Troubleshooting, Network Security, Network Design          High School   \n", "\n", "   experience_years              language  \n", "0                 5          French Basic  \n", "1                 9         French Fluent  \n", "2                 1  English Intermediate  \n", "3                 2        Chinese Fluent  \n", "4                10             German B1  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_sql_query(\"SELECT * FROM candidates\", conn)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "487aa116", "metadata": {}, "outputs": [], "source": ["from langchain_community.utilities import SQLDatabase"]}, {"cell_type": "code", "execution_count": 10, "id": "d60b9c6c", "metadata": {}, "outputs": [], "source": ["db = SQLDatabase.from_uri(\n", "    \"sqlite:///databases/candidates_details_500.db\")"]}, {"cell_type": "code", "execution_count": 11, "id": "a81c5fb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dialect: sqlite\n", "Usable tables: ['candidates', 'sqlite_sequence']\n"]}], "source": ["print(\"Dialect:\", db.dialect)\n", "print(\"Usable tables:\", db.get_usable_table_names())"]}, {"cell_type": "code", "execution_count": 12, "id": "aebc2273", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query result from Employees candidates: \n", " [(1, '<PERSON><PERSON><PERSON>', 47, '<PERSON>', 'AI Engineer', 'Robotics, Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 5, 'French Basic'), (2, '<PERSON>', 56, 'Portugal', 'Office Driver', None, 'Certificate Program', 9, 'French Fluent'), (3, '<PERSON>', 43, 'Swaziland', 'Office Driver', <PERSON>, 'B Tech', 1, 'English Intermediate'), (4, '<PERSON>', 25, 'Tunisia', 'Software Developer', 'C++, Python, JavaScript', 'BSc Nursing', 2, 'Chinese Fluent'), (5, '<PERSON>', 60, 'Tunisia', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'High School', 10, 'German B1'), (6, '<PERSON>', 30, 'Sri Lanka', 'Office Driver', None, None, 8, 'Spanish Intermediate'), (7, '<PERSON>', 49, 'Bosnia and Herzegovina', '<PERSON>', None, \"Master's Degree\", 19, None), (8, '<PERSON>', 31, '<PERSON>', 'Cybersecurity Specialist', 'Incident Response', \"Master's Degree\", 3, 'German C2'), (9, '<PERSON>', 32, '<PERSON><PERSON><PERSON>', '<PERSON>', <PERSON>, 'BSc Nursing', 5, None), (10, '<PERSON>', 37, '<PERSON>', '<PERSON><PERSON>', None, \"Bachelor's <PERSON>\", 9, 'English Fluent'), (11, '<PERSON> <PERSON><PERSON>', 53, 'Nigeria', 'Movie Director', <PERSON>, 'Associate Degree', 13, 'Spanish Basic'), (12, '<PERSON> <PERSON>', 43, '<PERSON>', 'Nurse', 'Wound Care, <PERSON>-Term Care, <PERSON>', \"Master's Degree\", 16, 'Chinese Basic'), (13, '<PERSON> <PERSON>', 51, '<PERSON>', '<PERSON> Scientist', '<PERSON> <PERSON>', \"Master's Degree\", 3, 'Spanish Intermediate'), (14, 'Daniel Harrington', 49, 'Dominican Republic', 'Teacher', None, 'High School', 5, 'Chinese Basic'), (15, 'Amy Alexander', 50, 'Guam', 'Movie Director', None, 'BSc Nursing', 10, 'Chinese Basic'), (16, 'Keith Krueger', 56, 'Afghanistan', 'Test Engineer', None, None, 15, 'Spanish Intermediate'), (17, 'William Thompson', 47, 'Andorra', 'Movie Director', None, 'PHD', 14, None), (18, 'Diana Foster', 42, 'Bosnia and Herzegovina', 'UX/UI Designer', 'Wireframing', 'Certificate Program', 5, None), (19, 'Martin Bright', 56, 'Pakistan', 'Engineer', None, None, 5, 'German B2'), (20, 'Alicia Webster', 39, 'Russian Federation', 'Cricketer', None, 'Commercial License', 3, None), (21, 'Heather Gardner', 54, 'Saint Martin', 'Nurse', 'ICU, Pediatrics, Long-Term Care, Emergency, Surgery', 'High School', 9, 'French Fluent'), (22, 'Derek Hardy', 34, 'Grenada', 'Professor', None, 'B Tech', 12, 'Spanish Intermediate'), (23, 'Laura Thomas', 51, 'Jamaica', 'Singer', None, 'M Tech', 8, None), (24, 'Carrie Hill', 59, 'Somalia', 'Software Developer', None, 'M Tech', 7, 'Chinese Intermediate'), (25, 'Brooke Pham', 48, 'Svalbard & Jan Mayen Islands', 'Teacher', None, \"Master's Degree\", 10, 'Spanish Basic'), (26, 'Cynthia Maldonado', 57, 'Ecuador', 'Movie Director', None, 'M Tech', 15, None), (27, 'Julia Rangel', 58, 'Bhutan', 'Database Administrator', None, 'Certificate Program', 1, None), (28, 'Jeffrey Dawson', 27, 'Dominican Republic', 'Gym Trainer', None, None, 17, 'English Fluent'), (29, 'Jacob Reynolds', 44, 'Sierra Leone', 'Gym Trainer', None, None, 19, 'English Basic'), (30, 'Ruth Sullivan', 54, 'Sri Lanka', 'Movie Director', None, None, 19, 'English Intermediate'), (31, 'Cynthia Howell', 54, 'Aruba', 'Movie Director', None, 'Commercial License', 2, 'French Intermediate'), (32, 'Samantha Clark', 54, 'Saint Helena', 'Athelete', None, 'M Tech', 20, 'English Intermediate'), (33, 'Jeffery Gilbert', 34, 'Svalbard & Jan Mayen Islands', 'UX/UI Designer', 'Prototyping, Wireframing', None, 18, 'French Fluent'), (34, 'Cindy Gibbs', 54, 'Chile', 'Cricketer', None, \"Bachelor's Degree\", 18, 'English Fluent'), (35, 'Elizabeth Smith DDS', 46, 'Pakistan', 'Licensed Practical Nurse', None, 'M Tech', 19, None), (36, 'Matthew Santos', 27, 'Bangladesh', 'Product Manager', 'Project Management, Market Research, Product Development', \"Master's Degree\", 15, 'Spanish Intermediate'), (37, 'Megan Snow', 26, 'Aruba', 'Cricketer', None, \"Master's Degree\", 10, 'German A2'), (38, 'Michelle Vargas', 44, 'Hong Kong', 'Movie Director', None, None, 7, 'Spanish Intermediate'), (39, 'Benjamin Cruz', 25, 'Russian Federation', 'Professor', None, 'Commercial License', 19, 'English Basic'), (40, 'Angela Ramsey', 44, 'Anguilla', 'Gym Trainer', None, 'Commercial License', 18, 'English Fluent'), (41, 'Robert Murillo', 39, 'New Caledonia', 'Singer', None, 'Certificate Program', 18, 'English Basic'), (42, 'Edward Boyd', 33, 'Madagascar', 'Movie Producer', None, 'Associate Degree', 10, 'German C1'), (43, 'Jamie Williams', 48, 'Pakistan', 'Professor', None, 'Diploma', 6, 'English Intermediate'), (44, 'Ann Everett', 25, 'Norfolk Island', 'Dancer', None, None, 12, 'Spanish Intermediate'), (45, 'Carolyn Franklin', 40, 'Greenland', 'Test Engineer', None, 'M Tech', 20, None), (46, 'Teresa Greene', 54, 'Saint Vincent and the Grenadines', 'School Bus Driver', None, 'BSc Nursing', 13, 'Spanish Intermediate'), (47, 'Heather White', 28, 'Gibraltar', 'Engineer', 'Civil Engineering, Chemical Engineering, Electrical Engineering, Mechanical Engineering', None, 11, 'Spanish Fluent'), (48, 'Chris Price', 22, 'Puerto Rico', 'Gym Trainer', None, \"Bachelor's Degree\", 17, 'Spanish Basic'), (49, 'Anne Long', 51, 'British Virgin Islands', 'Teacher', None, 'Diploma', 2, 'English Intermediate'), (50, 'John Bradford', 40, 'Lesotho', 'Test Engineer', None, 'B Tech', 19, 'French Intermediate'), (51, 'Hunter Miller', 22, 'Mexico', 'Dancer', None, \"Master's Degree\", 6, 'English Intermediate'), (52, 'Michelle Roberts', 51, 'Bermuda', 'Engineer', 'Civil Engineering, Chemical Engineering', None, 11, 'Spanish Intermediate'), (53, 'Kyle Richardson', 42, 'Ecuador', 'Dancer', None, 'Diploma', 11, 'French Basic'), (54, 'Hannah Smith', 57, 'Sao Tome and Principe', 'DevOps Engineer', 'Cloud Computing, Containerization', 'Commercial License', 10, None), (55, 'John Gonzalez', 52, 'Jersey', 'Gym Trainer', None, 'Commercial License', 12, 'Chinese Basic'), (56, 'Latoya Ramsey', 41, 'Libyan Arab Jamahiriya', 'Social Media Influencer', None, None, 9, 'German C2'), (57, 'Sarah Jones', 49, 'South Africa', 'Cricketer', None, 'BSc Nursing', 15, None), (58, 'Jill Phillips', 58, 'Cape Verde', 'Professor', None, \"Bachelor's Degree\", 16, 'Spanish Basic'), (59, 'Amy Rodriguez', 26, \"Lao People's Democratic Republic\", 'Social Media Influencer', None, \"Bachelor's Degree\", 4, 'French Fluent'), (60, 'Eugene Bonilla', 26, 'Mauritius', 'Office Driver', None, None, 6, None), (61, 'Jennifer Bass MD', 58, 'Northern Mariana Islands', 'Test Engineer', None, None, 19, None), (62, 'John Carey', 29, 'Ireland', 'Product Manager', 'Project Management, Product Development, Market Research', 'High School', 16, None), (63, 'Regina Mckenzie', 22, 'Hungary', 'Software Developer', None, 'BSc Nursing', 9, 'German B2'), (64, 'Terri Harris', 50, 'Chile', 'Pilot', None, 'B Tech', 18, None), (65, 'Daniel Hawkins', 32, 'Malta', 'Cybersecurity Specialist', None, None, 1, 'Chinese Intermediate'), (66, 'Sally Curry', 22, 'Georgia', 'Professor', None, None, 2, 'Spanish Intermediate'), (67, 'Suzanne Delgado', 52, 'Greenland', 'Database Administrator', 'Backup and Recovery', 'BSc Nursing', 2, 'English Basic'), (68, 'Tiffany Jones', 26, 'Western Sahara', 'Office Driver', None, 'BSc Nursing', 9, 'English Basic'), (69, 'Carolyn Mahoney', 25, 'Lithuania', 'Movie Producer', None, 'High School', 9, 'German B1'), (70, 'Kimberly Hobbs', 60, 'Saint Kitts and Nevis', 'Dancer', None, 'B Tech', 1, None), (71, 'Tristan Warren', 34, 'Mozambique', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'M Tech', 17, 'English Intermediate'), (72, 'Samantha Jacobson', 28, 'Guam', 'Data Scientist', 'Statistics, Machine Learning, Data Analysis, Data Visualization', 'M Tech', 16, 'Spanish Fluent'), (73, 'Heidi House', 58, 'United Kingdom', 'Nurse', 'Pediatrics, Surgery', 'BSc Nursing', 12, None), (74, 'Chelsea Collins', 36, 'Lesotho', 'Singer', None, 'PHD', 1, 'Chinese Fluent'), (75, 'Jason Moreno', 55, 'Togo', 'Database Administrator', 'Performance Tuning, Backup and Recovery, Database Design', None, 16, 'French Basic'), (76, 'Jack Reyes', 43, 'United Arab Emirates', 'Athelete', None, 'BSc Nursing', 17, 'French Fluent'), (77, 'Lisa Randall', 32, 'United States Minor Outlying Islands', 'Engineer', 'Electrical Engineering, Mechanical Engineering, Chemical Engineering', None, 5, 'German C2'), (78, 'James Wilkinson', 53, 'Cuba', 'Product Manager', 'Product Development, Market Research', 'PHD', 2, None), (79, 'Jeffrey Salazar', 51, 'Norway', 'Engineer', 'Mechanical Engineering, Chemical Engineering, Civil Engineering, Electrical Engineering', 'PHD', 9, 'Chinese Fluent'), (80, 'Heather Lynn', 47, 'Qatar', 'Pilot', None, None, 16, 'Chinese Basic'), (81, 'Meghan Martin', 47, 'Cambodia', 'Software Developer', 'JavaScript, C++, Python, Java, Agile Development', 'Diploma', 15, 'French Basic'), (82, 'William Foster', 60, 'Grenada', 'Software Developer', None, 'Diploma', 5, 'English Fluent'), (83, 'Reginald Williams', 59, 'Aruba', 'Dancer', None, 'B Tech', 17, 'German A1'), (84, 'John Ali', 39, 'Egypt', 'Painter', None, None, 15, 'German B2'), (85, 'Laura Holt', 30, 'Vietnam', 'Licensed Practical Nurse', 'Medication Administration', None, 11, 'Spanish Intermediate'), (86, 'Ashley Wolf', 51, 'Namibia', 'Gym Trainer', None, 'BSc Nursing', 10, 'Chinese Fluent'), (87, 'Jennifer Suarez', 44, 'Barbados', 'Singer', None, 'PHD', 10, 'Chinese Basic'), (88, 'Alexander Smith', 36, 'Haiti', 'Network Administrator', None, None, 3, None), (89, 'Ian Hoffman', 36, 'Tanzania', 'Pilot', None, 'BSc Nursing', 9, None), (90, 'Louis Hernandez', 25, 'Moldova', 'Social Media Influencer', None, 'Certificate Program', 11, None), (91, 'Terri Gray', 25, 'Saint Helena', 'AI Engineer', 'Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 11, None), (92, 'Diane Ramirez', 28, 'Singapore', 'Painter', None, \"Master's Degree\", 3, 'Chinese Basic'), (93, 'Megan Anderson', 27, 'Brazil', 'Cybersecurity Specialist', None, None, 13, 'Chinese Basic'), (94, 'Andre Olson', 49, 'Qatar', 'Cybersecurity Specialist', None, 'Certificate Program', 6, 'Chinese Intermediate'), (95, 'Ian Huang', 40, 'New Zealand', 'Product Manager', 'Project Management, Market Research', 'Commercial License', 8, 'German C2'), (96, 'Kelly Gonzalez', 56, 'Tokelau', 'Pilot', None, None, 5, 'German C2'), (97, 'Bruce Smith', 22, 'Grenada', 'Singer', None, 'Diploma', 6, 'Spanish Intermediate'), (98, 'Renee Cooper', 49, 'American Samoa', 'Test Engineer', None, \"Bachelor's Degree\", 4, 'Spanish Fluent'), (99, 'John Hall', 51, 'Holy See (Vatican City State)', 'Engineer', None, 'PHD', 19, 'English Basic'), (100, 'Jennifer Sullivan', 45, 'Trinidad and Tobago', 'Dancer', None, 'Diploma', 11, 'French Basic'), (101, 'Jeffrey Orozco', 60, 'Niger', 'Software Developer', 'Agile Development', 'M Tech', 7, 'English Intermediate'), (102, 'Timothy Green', 40, 'Malawi', 'Movie Producer', None, \"Master's Degree\", 17, None), (103, 'Nancy Davidson', 23, 'Suriname', 'Athelete', None, 'BSc Nursing', 15, 'French Fluent'), (104, 'Marcus Jones', 27, 'Faroe Islands', 'Product Manager', None, 'Commercial License', 19, 'Spanish Fluent'), (105, 'Cole Wright', 24, 'Timor-Leste', 'Professor', None, None, 18, None), (106, 'Mark Schaefer', 59, 'Czech Republic', 'Licensed Practical Nurse', None, \"Master's Degree\", 2, 'German C2'), (107, 'Amy Kelly', 46, 'United Kingdom', 'UX/UI Designer', 'Usability Testing, User Research', \"Bachelor's Degree\", 14, 'Chinese Intermediate'), (108, 'Amanda Thompson', 30, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, None, 16, 'German B1'), (109, 'Michelle Hines', 46, 'Iran', 'School Bus Driver', 'Defensive Driving', 'PHD', 5, 'French Intermediate'), (110, 'Ronald Barnett', 25, 'Cuba', 'Movie Director', None, 'BSc Nursing', 7, 'German B1'), (111, 'Peggy Garcia', 23, 'Kenya', 'Data Scientist', 'Statistics, Data Analysis, Machine Learning', 'Diploma', 18, 'Chinese Fluent'), (112, 'Mark Miller', 26, 'Iceland', 'Cybersecurity Specialist', 'Compliance, Incident Response, Threat Analysis', 'M Tech', 20, 'English Fluent'), (113, 'Katie Gonzalez', 49, 'Anguilla', 'Database Administrator', None, None, 12, 'Chinese Fluent'), (114, 'Lisa Beltran', 52, 'Antarctica (the territory South of 60 deg S)', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'PHD', 6, 'German A2'), (115, 'Janice Ross', 54, 'Vanuatu', 'UX/UI Designer', None, None, 6, 'Spanish Fluent'), (116, 'Jeremiah Moore', 43, 'Vietnam', 'Dancer', None, 'Commercial License', 8, 'English Fluent'), (117, 'Kaitlyn Wilson', 45, 'Hong Kong', 'Singer', None, None, 2, 'German A2'), (118, 'Margaret Patterson', 23, 'Djibouti', 'Singer', None, 'Commercial License', 3, 'German C2'), (119, 'Ethan Rice', 28, 'French Southern Territories', 'Gym Trainer', None, 'High School', 1, 'German C1'), (120, 'Jennifer Ritter', 24, 'Eritrea', 'AI Engineer', None, None, 17, None), (121, 'Paul Hubbard', 30, 'Italy', 'Cricketer', None, None, 1, 'French Fluent'), (122, 'Julie Patel', 38, 'Burundi', 'Dancer', None, \"Master's Degree\", 7, 'Chinese Basic'), (123, 'Craig Fisher', 43, 'New Zealand', 'UX/UI Designer', 'Prototyping, User Research', 'Certificate Program', 3, 'Chinese Intermediate'), (124, 'Andrea Ramsey', 58, 'Western Sahara', 'Software Developer', 'Python, Java, JavaScript', 'PHD', 13, 'English Fluent'), (125, 'Christian Wallace', 50, 'Ghana', 'Nurse', 'Pediatrics', \"Master's Degree\", 10, 'Chinese Fluent'), (126, 'Lauren Harrell', 32, 'Yemen', 'DevOps Engineer', 'Containerization, Cloud Computing', \"Bachelor's Degree\", 18, 'French Fluent'), (127, 'Vincent Hernandez', 34, 'Bahamas', 'Pilot', None, \"Bachelor's Degree\", 15, None), (128, 'Christina Brown', 33, 'Eritrea', 'Cricketer', None, 'PHD', 17, 'English Basic'), (129, 'Ronald Harris', 26, 'Isle of Man', 'Truck Driver', 'OTR, Tanker', 'BSc Nursing', 4, 'English Intermediate'), (130, 'Kelly Coleman', 44, 'Angola', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'Associate Degree', 4, 'Spanish Intermediate'), (131, 'Amanda Taylor', 31, 'Mozambique', 'Data Scientist', 'Statistics, Machine Learning', 'PHD', 13, 'English Basic'), (132, 'Mark Foster', 49, 'Andorra', 'Test Engineer', None, None, 6, None), (133, 'James Marsh', 43, 'Saint Pierre and Miquelon', 'AI Engineer', 'Deep Learning, Natural Language Processing, Robotics', 'B Tech', 10, 'Spanish Basic'), (134, 'Judith Dunn', 31, 'Tuvalu', 'Singer', None, 'High School', 11, 'English Basic'), (135, 'Michael Brown', 43, 'Estonia', 'Database Administrator', 'Database Design', \"Bachelor's Degree\", 7, 'Spanish Fluent'), (136, 'Vicki Smith', 25, 'Uganda', 'Software Developer', 'JavaScript', 'Commercial License', 11, 'German C2'), (137, 'Jason Soto', 50, 'Burundi', 'UX/UI Designer', 'Prototyping, Usability Testing', 'BSc Nursing', 16, 'French Fluent'), (138, 'Margaret Dougherty', 33, 'Romania', 'Network Administrator', 'Troubleshooting, Network Design', None, 6, 'German C1'), (139, 'James White', 49, 'Saint Martin', 'Professor', None, None, 10, 'Chinese Basic'), (140, 'Andrew Ortiz', 48, 'Central African Republic', 'AI Engineer', None, 'PHD', 10, 'Chinese Fluent'), (141, 'Crystal David DVM', 42, 'French Polynesia', 'UX/UI Designer', 'Usability Testing, User Research, Wireframing, Prototyping', 'Commercial License', 13, 'Spanish Fluent'), (142, 'Brenda Williams', 57, 'Kenya', 'DevOps Engineer', 'Monitoring, CI/CD, Containerization, Cloud Computing', 'PHD', 14, 'Spanish Intermediate'), (143, 'Douglas Webster', 48, 'Saint Helena', 'Singer', None, 'PHD', 3, 'French Fluent'), (144, 'Terry Elliott', 48, 'Turks and Caicos Islands', 'Teacher', None, 'Commercial License', 1, 'Spanish Fluent'), (145, 'Melissa Ortega', 30, 'Central African Republic', 'Test Engineer', None, 'M Tech', 11, None), (146, 'Karen Rodriguez PhD', 48, 'Sierra Leone', 'Nurse', 'Surgery, Wound Care, Pediatrics', 'Diploma', 7, None), (147, 'Troy Ryan', 26, 'Congo', 'Test Engineer', None, 'Diploma', 14, 'Chinese Fluent'), (148, 'Jeremy Stein', 32, 'Zimbabwe', 'AI Engineer', None, 'BSc Nursing', 15, 'English Basic'), (149, 'Daniel Miller', 23, 'Monaco', 'Cricketer', None, None, 14, 'Chinese Fluent'), (150, 'Cheryl Reed', 30, 'Colombia', 'Cricketer', None, 'Diploma', 8, 'English Fluent'), (151, 'Eric Holland', 35, 'Oman', 'Network Administrator', 'Network Design, Network Security', 'Diploma', 2, 'Chinese Intermediate'), (152, 'Austin Velez', 46, 'Isle of Man', 'Network Administrator', 'Network Design', 'Commercial License', 18, 'German B1'), (153, 'Kelly Lopez', 56, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, 'M Tech', 20, 'French Intermediate'), (154, 'Michael Christian', 23, 'Gibraltar', 'Data Scientist', 'Data Visualization, Machine Learning, Data Analysis, Statistics', 'Associate Degree', 8, None), (155, 'Tiffany Johnson', 58, 'Saudi Arabia', 'Data Scientist', 'Data Analysis', 'Certificate Program', 19, 'Spanish Basic'), (156, 'Stephen Church', 45, 'Saint Lucia', 'Data Scientist', 'Data Visualization, Data Analysis, Machine Learning, Statistics', 'M Tech', 20, 'Spanish Fluent'), (157, 'Lauren Larsen DDS', 26, 'Romania', 'Engineer', 'Civil Engineering', 'Commercial License', 16, 'German C1'), (158, 'Stacy Nicholson', 55, 'Tokelau', 'Dancer', None, 'Certificate Program', 15, 'German B2'), (159, 'Jessica Bean', 60, 'Gambia', 'Database Administrator', 'Backup and Recovery', 'PHD', 19, 'Chinese Fluent'), (160, 'Sheri Anderson', 30, 'Eritrea', 'Painter', None, 'PHD', 8, 'German A1'), (161, 'Sarah Dixon', 45, 'Namibia', 'Pilot', None, None, 14, 'French Fluent'), (162, 'Michele Davis', 28, 'Central African Republic', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 9, 'Chinese Intermediate'), (163, 'Glenn Mejia', 52, 'Antarctica (the territory South of 60 deg S)', 'Office Driver', None, 'BSc Nursing', 3, 'English Intermediate'), (164, 'Susan Riggs', 28, 'Cameroon', 'Teacher', None, None, 18, 'French Fluent'), (165, 'Lori Franklin PhD', 25, 'Lebanon', 'AI Engineer', 'Natural Language Processing', None, 11, 'Chinese Basic'), (166, 'Michelle Saunders', 53, 'Niue', 'Movie Producer', None, 'High School', 2, 'French Intermediate'), (167, 'Brian Wise', 47, 'Palestinian Territory', 'UX/UI Designer', 'Usability Testing, User Research, Prototyping', 'Diploma', 10, 'German C2'), (168, 'Ryan Miller', 22, 'Bahamas', 'Athelete', None, 'Associate Degree', 2, None), (169, 'Christopher Lopez', 28, 'Montenegro', 'DevOps Engineer', None, 'M Tech', 3, None), (170, 'Benjamin Scott', 38, 'Suriname', 'Database Administrator', None, 'PHD', 15, 'French Basic'), (171, 'Mark Carson', 53, 'Nicaragua', 'Cricketer', None, \"Master's Degree\", 14, 'German B2'), (172, 'Helen Simmons', 38, 'Isle of Man', 'Cricketer', None, \"Master's Degree\", 16, 'Spanish Basic'), (173, 'Krista Moore', 36, 'Cambodia', 'Test Engineer', None, 'Certificate Program', 13, 'German C1'), (174, 'Jonathan Hernandez', 37, 'South Georgia and the South Sandwich Islands', 'Teacher', None, 'PHD', 8, 'Chinese Intermediate'), (175, 'Lisa Griffin', 30, 'Bouvet Island (Bouvetoya)', 'Data Scientist', 'Data Analysis, Statistics', None, 1, None), (176, 'Beth Espinoza', 52, 'Tajikistan', 'Nurse', 'Emergency', None, 19, 'Spanish Fluent'), (177, 'Terry Aguirre', 56, 'Cyprus', 'Cricketer', None, 'Commercial License', 15, 'English Basic'), (178, 'Mr. Richard Smith', 34, 'Palau', 'Teacher', None, \"Master's Degree\", 2, 'Chinese Basic'), (179, 'Phillip Ruiz', 25, 'Sweden', 'Test Engineer', None, 'Certificate Program', 15, None), (180, 'Maria Brooks', 53, 'Albania', 'Painter', None, None, 15, 'Chinese Intermediate'), (181, 'Mark Hall', 49, 'Holy See (Vatican City State)', 'Cybersecurity Specialist', None, 'High School', 16, 'Chinese Intermediate'), (182, 'Karen Welch', 41, 'United Arab Emirates', 'Singer', None, 'Certificate Program', 20, 'French Fluent'), (183, 'Alyssa Howard', 36, 'Heard Island and McDonald Islands', 'Software Developer', 'Agile Development, Python, Java, JavaScript', \"Master's Degree\", 11, 'Chinese Intermediate'), (184, 'Mr. Jason Smith', 47, 'Estonia', 'Engineer', 'Civil Engineering', 'M Tech', 14, None), (185, 'Robert Baker', 30, 'Taiwan', 'Movie Producer', None, 'B Tech', 19, 'English Basic'), (186, 'Robert Scott', 49, 'Spain', 'Painter', None, 'Commercial License', 6, 'English Basic'), (187, 'Destiny Graves', 39, 'Hong Kong', 'Engineer', 'Chemical Engineering, Civil Engineering, Electrical Engineering, Mechanical Engineering', \"Bachelor's Degree\", 5, 'French Basic'), (188, 'James Johns', 52, 'Spain', 'Singer', None, 'PHD', 11, 'Chinese Basic'), (189, 'David Boyd', 26, 'Ecuador', 'Social Media Influencer', None, \"Bachelor's Degree\", 14, 'German A1'), (190, 'Angela Woods', 30, 'Greenland', 'Engineer', 'Electrical Engineering', 'Diploma', 11, 'German A2'), (191, 'James Novak', 35, 'Kenya', 'Painter', None, 'B Tech', 4, 'German B1'), (192, 'Phillip Hogan', 27, \"Cote d'Ivoire\", 'Dancer', None, 'M Tech', 11, None), (193, 'Alexandra Black', 31, 'Chad', 'Cricketer', None, 'BSc Nursing', 19, 'Spanish Basic'), (194, 'Megan Newman', 54, 'Cayman Islands', 'Dancer', None, None, 11, None), (195, 'Andre Griffin', 28, 'Western Sahara', 'Nurse', 'ICU, Surgery, Wound Care, Emergency, Long-Term Care, Pediatrics', 'Associate Degree', 20, 'Chinese Fluent'), (196, 'Jean Drake', 59, 'Switzerland', 'School Bus Driver', 'Passenger Endorsement', 'BSc Nursing', 18, 'Spanish Basic'), (197, 'Regina Price', 39, 'Liechtenstein', 'Pilot', None, None, 10, 'English Basic'), (198, 'Nancy Cortez', 45, 'Western Sahara', 'Gym Trainer', None, None, 16, None), (199, 'Natalie Howard', 35, 'Aruba', 'Software Developer', 'Python, C++', 'Certificate Program', 6, 'German A1'), (200, 'Shawn Richardson', 40, 'Togo', 'Teacher', None, \"Master's Degree\", 11, 'Spanish Intermediate'), (201, 'Dr. Emily Scott DDS', 37, 'Kiribati', 'Licensed Practical Nurse', None, \"Master's Degree\", 7, 'English Basic'), (202, 'Michelle Lopez', 33, 'Trinidad and Tobago', 'Network Administrator', None, 'B Tech', 13, 'Chinese Fluent'), (203, 'Caitlin Buchanan', 25, 'Cook Islands', 'Movie Producer', None, None, 2, 'German A1'), (204, 'Tara Smith', 35, 'Congo', 'Dancer', None, \"Bachelor's Degree\", 8, 'Spanish Fluent'), (205, 'Heather Patel', 26, 'Niue', 'DevOps Engineer', 'Containerization, Monitoring, CI/CD, Cloud Computing', 'M Tech', 11, 'Spanish Intermediate'), (206, 'Kevin Mckinney', 47, 'Timor-Leste', 'Nurse', None, 'M Tech', 8, 'English Basic'), (207, 'Kim Martin', 22, 'Saint Vincent and the Grenadines', 'Dancer', None, 'B Tech', 18, 'French Intermediate'), (208, 'Hunter Morgan', 54, 'Congo', 'Test Engineer', None, 'B Tech', 16, 'English Basic'), (209, 'Jonathan Washington', 45, 'Equatorial Guinea', 'UX/UI Designer', None, 'PHD', 15, 'English Basic'), (210, 'Nathaniel Sanchez', 26, 'Burkina Faso', 'Database Administrator', 'Backup and Recovery, Performance Tuning, Database Design', 'Commercial License', 12, 'Spanish Intermediate'), (211, 'Theresa Murillo', 35, 'Romania', 'Truck Driver', None, 'Certificate Program', 17, 'Chinese Intermediate'), (212, 'Carrie Jackson', 59, 'Tanzania', 'Nurse', 'Pediatrics, Long-Term Care, Surgery, ICU', \"Bachelor's Degree\", 15, 'English Fluent'), (213, 'Melissa Perez', 55, 'Cyprus', 'Social Media Influencer', None, \"Bachelor's Degree\", 7, 'Chinese Fluent'), (214, 'Michael Moreno', 60, 'Ethiopia', 'School Bus Driver', 'Passenger Endorsement', 'High School', 20, 'Spanish Basic'), (215, 'Kerry Jones', 45, 'Fiji', 'Dancer', None, 'M Tech', 12, 'Spanish Intermediate'), (216, 'Beth Austin', 28, \"Lao People's Democratic Republic\", 'Dancer', None, \"Master's Degree\", 2, 'German B2'), (217, 'Jon Ewing', 56, 'Nauru', 'Painter', None, \"Bachelor's Degree\", 7, 'Spanish Fluent'), (218, 'Jason Kane', 39, 'Djibouti', 'Test Engineer', None, None, 16, 'French Basic'), (219, 'Scott Patel', 54, 'Bolivia', 'Office Driver', None, 'M Tech', 20, 'English Intermediate'), (220, 'Mrs. Lisa Warren', 36, 'Solomon Islands', 'Test Engineer', None, 'Commercial License', 15, 'German B1'), (221, 'Bill Bowers', 36, 'Tunisia', 'Dancer', None, None, 15, 'French Basic'), (222, 'Maria Ortiz', 55, 'Turkey', 'Gym Trainer', None, 'Commercial License', 20, 'Chinese Intermediate'), (223, 'Robert Warner', 24, 'Wallis and Futuna', 'Movie Director', None, 'Diploma', 3, None), (224, 'David Nelson', 39, 'Angola', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'Diploma', 13, None), (225, 'Donna Meyers', 40, 'Monaco', 'Athelete', None, None, 1, 'Spanish Intermediate'), (226, 'Oscar Velez', 32, 'Norfolk Island', 'Cricketer', None, None, 11, 'Chinese Fluent'), (227, 'John Long', 54, 'Estonia', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Certificate Program', 12, 'Spanish Intermediate'), (228, 'Susan Sherman', 43, 'Saint Kitts and Nevis', 'Dancer', None, 'High School', 4, None), (229, 'Kristin Levine', 46, 'United States of America', 'Social Media Influencer', None, \"Master's Degree\", 11, 'German A1'), (230, 'Philip Swanson', 41, 'Sierra Leone', 'Data Scientist', 'Data Visualization, Statistics, Data Analysis', 'Commercial License', 9, 'German B2'), (231, 'Theodore Carter', 57, 'Bosnia and Herzegovina', 'Engineer', None, 'High School', 12, 'German A2'), (232, 'Nathan Crawford MD', 50, 'Turkmenistan', 'Athelete', None, \"Master's Degree\", 9, 'Spanish Fluent'), (233, 'Jimmy Neal', 44, 'Turks and Caicos Islands', 'School Bus Driver', None, 'PHD', 5, 'French Intermediate'), (234, 'Jessica Tapia', 26, 'Namibia', 'Licensed Practical Nurse', None, 'B Tech', 7, None), (235, 'Ricky Noble', 35, 'Norway', 'Professor', None, 'High School', 9, 'English Basic'), (236, 'Michael Hudson', 59, 'Poland', 'Engineer', None, 'Certificate Program', 5, 'Spanish Fluent'), (237, 'Yolanda Doyle', 37, 'Tokelau', 'DevOps Engineer', 'Monitoring', 'Certificate Program', 15, 'English Intermediate'), (238, 'Amy Scott', 54, 'Paraguay', 'Engineer', None, 'M Tech', 1, 'German C1'), (239, 'Frances Dean', 29, 'Cocos (Keeling) Islands', 'Software Developer', 'Agile Development, Java, Python', \"Bachelor's Degree\", 16, 'German B2'), (240, 'Robert Griffith', 49, 'El Salvador', 'UX/UI Designer', 'Wireframing, User Research', 'PHD', 16, 'English Basic'), (241, 'John Lawson', 23, 'Grenada', 'Dancer', None, None, 12, 'Chinese Basic'), (242, 'Mrs. Stefanie Finley', 42, 'Angola', 'DevOps Engineer', None, 'High School', 5, 'Chinese Fluent'), (243, 'Jenna Mcknight', 33, 'Samoa', 'UX/UI Designer', 'User Research, Prototyping', None, 8, 'Chinese Fluent'), (244, 'Tracy Green', 39, 'Burkina Faso', 'School Bus Driver', None, 'Certificate Program', 4, 'Spanish Basic'), (245, 'Nicholas Dunn', 57, 'Oman', 'Cybersecurity Specialist', 'Threat Analysis', 'BSc Nursing', 13, 'English Fluent'), (246, 'Abigail Taylor', 30, 'Haiti', 'Teacher', None, 'Diploma', 14, 'German A2'), (247, 'Kelly Hall', 50, 'Costa Rica', 'Painter', None, 'B Tech', 11, 'Chinese Intermediate'), (248, 'Ebony Lopez', 42, 'Italy', 'Cricketer', None, None, 15, 'Chinese Fluent'), (249, 'Katie Beck', 43, 'Honduras', 'AI Engineer', None, \"Bachelor's Degree\", 9, None), (250, 'Marie Peters', 34, 'Suriname', 'Test Engineer', None, 'PHD', 19, 'German B1'), (251, 'Toni Short', 39, 'Comoros', 'Engineer', 'Civil Engineering', 'B Tech', 13, None), (252, 'Richard Rose', 52, 'Grenada', 'Truck Driver', 'Hazmat, Tanker, Defensive Driving, OTR', 'Diploma', 8, 'Chinese Basic'), (253, 'Christopher Bentley', 42, 'Timor-Leste', 'Painter', None, 'High School', 12, 'Spanish Fluent'), (254, 'William Neal', 38, 'Denmark', 'Data Scientist', 'Statistics, Data Visualization', 'Commercial License', 11, None), (255, 'Preston Savage', 54, 'Morocco', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'Certificate Program', 18, None), (256, 'Philip Ryan', 54, 'Botswana', 'Truck Driver', 'OTR, Defensive Driving, Hazmat, Tanker', 'Certificate Program', 20, 'English Basic'), (257, 'Jennifer Moore', 37, 'Israel', 'Data Scientist', None, 'Certificate Program', 2, None), (258, 'Kelly Gonzalez', 51, 'Guadeloupe', 'Database Administrator', None, 'High School', 11, 'Chinese Fluent'), (259, 'Michael Young', 52, 'Mali', 'Database Administrator', 'Database Design', 'PHD', 20, 'Spanish Basic'), (260, 'Lynn Welch', 41, 'Zambia', 'Network Administrator', 'Network Design', None, 9, 'English Fluent'), (261, 'Dana Smith', 59, 'Norway', 'Dancer', None, 'Diploma', 11, 'Spanish Intermediate'), (262, 'Andrea Sanchez', 32, 'Belize', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', \"Bachelor's Degree\", 7, 'Chinese Basic'), (263, 'Maurice Murray', 29, 'Ireland', 'Engineer', None, 'PHD', 7, 'German C2'), (264, 'Bill Collier', 29, 'Western Sahara', 'Athelete', None, \"Master's Degree\", 4, 'English Fluent'), (265, 'Aaron Hernandez', 28, 'Saudi Arabia', 'Cybersecurity Specialist', 'Threat Analysis', None, 11, None), (266, 'Suzanne Cooper', 46, 'Latvia', 'Cybersecurity Specialist', 'Network Security, Threat Analysis, Compliance', 'Associate Degree', 3, 'English Intermediate'), (267, 'Jeremy Taylor', 56, 'Equatorial Guinea', 'Office Driver', None, \"Bachelor's Degree\", 19, 'French Intermediate'), (268, 'Christopher Coleman', 34, 'Saint Pierre and Miquelon', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis', 'Commercial License', 2, 'German A1'), (269, 'Sara Garcia', 32, 'Somalia', 'Product Manager', None, 'BSc Nursing', 2, None), (270, 'Angel Lee', 56, 'Hungary', 'Truck Driver', None, 'Certificate Program', 16, None), (271, 'Jamie Sparks', 41, 'Philippines', 'Gym Trainer', None, 'Diploma', 10, 'German C2'), (272, 'Brian Mcgrath', 51, 'Bulgaria', 'Office Driver', None, 'Certificate Program', 12, None), (273, 'Christine Coleman', 28, 'Algeria', 'Nurse', 'Long-Term Care, Pediatrics, ICU', 'Associate Degree', 6, 'English Basic'), (274, 'Bryan Greene', 26, 'Norfolk Island', 'AI Engineer', 'Computer Vision, Deep Learning, Robotics', None, 14, None), (275, 'Mr. Cody Harper', 26, 'Suriname', 'Truck Driver', 'Defensive Driving, Tanker, Hazmat, OTR', 'Associate Degree', 17, None), (276, 'Margaret Brown', 53, 'Switzerland', 'Cybersecurity Specialist', 'Threat Analysis, Incident Response, Compliance, Network Security', None, 20, 'German B2'), (277, 'Jack Scott', 47, 'Netherlands', 'DevOps Engineer', 'CI/CD, Containerization', \"Master's Degree\", 3, 'German C1'), (278, 'Becky Stanley', 50, 'Tajikistan', 'Cybersecurity Specialist', 'Compliance, Network Security', None, 10, 'Chinese Fluent'), (279, 'Sean Durham', 29, 'Seychelles', 'Software Developer', None, None, 20, None), (280, 'Teresa Thomas', 60, 'China', 'Cricketer', None, 'Associate Degree', 12, None), (281, 'Chelsea Mccormick', 49, 'Costa Rica', 'Test Engineer', None, None, 12, None), (282, 'Jonathan Brooks', 44, 'Barbados', 'Painter', None, 'BSc Nursing', 10, 'English Intermediate'), (283, 'Michelle Coleman', 55, 'Costa Rica', 'Movie Producer', None, 'Certificate Program', 1, 'Spanish Basic'), (284, 'Gabriel Cox', 46, 'Aruba', 'Teacher', None, \"Master's Degree\", 18, 'English Fluent'), (285, 'Nicholas Tucker', 60, 'Brunei Darussalam', 'Movie Producer', None, 'High School', 2, 'Spanish Intermediate'), (286, 'Kendra Blevins', 32, 'Isle of Man', 'Software Developer', 'Python, Java, JavaScript, Agile Development, C++', 'B Tech', 20, 'Chinese Fluent'), (287, 'Courtney Schaefer', 44, 'Montserrat', 'DevOps Engineer', 'Containerization, CI/CD', 'BSc Nursing', 7, None), (288, 'Sarah Cain', 26, 'Sri Lanka', 'Cybersecurity Specialist', 'Incident Response, Threat Analysis, Network Security', 'B Tech', 15, 'German C2'), (289, 'James Ramos', 56, 'Guam', 'Social Media Influencer', None, 'Certificate Program', 15, 'Chinese Fluent'), (290, 'Ryan Frank', 53, 'Togo', 'Dancer', None, 'High School', 17, 'Chinese Fluent'), (291, 'Joel Farmer', 22, 'Sierra Leone', 'Gym Trainer', None, 'Certificate Program', 8, None), (292, 'John Medina', 46, 'Swaziland', 'Social Media Influencer', None, \"Bachelor's Degree\", 16, 'French Intermediate'), (293, 'Cheryl Allen', 29, 'Maldives', 'UX/UI Designer', 'User Research, Usability Testing, Prototyping', \"Bachelor's Degree\", 13, 'English Basic'), (294, 'Collin Evans', 41, 'Sri Lanka', 'Office Driver', None, \"Master's Degree\", 6, 'Spanish Fluent'), (295, 'Paul Soto', 60, 'Niue', 'Gym Trainer', None, 'PHD', 8, 'French Intermediate'), (296, 'Daniel Hall', 38, 'Antigua and Barbuda', 'Cricketer', None, 'M Tech', 18, 'French Basic'), (297, 'Kyle Clayton', 60, 'Croatia', 'Painter', None, \"Bachelor's Degree\", 5, 'English Basic'), (298, 'Elizabeth Ward', 43, 'Pakistan', 'Professor', None, None, 12, None), (299, 'Michael Perkins', 23, 'Kuwait', 'Professor', None, None, 18, 'Chinese Fluent'), (300, 'Katelyn Carroll', 45, 'Sierra Leone', 'Movie Producer', None, 'Certificate Program', 3, 'French Basic'), (301, 'William Banks', 46, 'Tunisia', 'Cybersecurity Specialist', 'Network Security, Compliance, Incident Response, Threat Analysis', 'Associate Degree', 14, 'English Fluent'), (302, 'Jose Powers', 25, 'Congo', 'Painter', None, 'Diploma', 20, 'French Fluent'), (303, 'Grace Hendricks', 58, 'Barbados', 'Data Scientist', 'Data Analysis, Data Visualization, Statistics, Machine Learning', 'B Tech', 9, None), (304, 'Sarah Blake', 27, 'Cayman Islands', 'Office Driver', None, 'High School', 1, 'English Intermediate'), (305, 'Richard Tran', 22, 'Christmas Island', 'Movie Director', None, 'Associate Degree', 7, 'Spanish Intermediate'), (306, 'Edwin Carlson', 22, 'Greece', 'AI Engineer', None, 'PHD', 5, 'English Basic'), (307, 'Nina Singh', 37, 'Cook Islands', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'High School', 19, 'Spanish Fluent'), (308, 'Kimberly Robinson', 39, 'Holy See (Vatican City State)', 'Test Engineer', None, 'Associate Degree', 9, 'Spanish Fluent'), (309, 'Michael Schwartz', 39, 'Sri Lanka', 'Database Administrator', None, 'M Tech', 17, 'English Intermediate'), (310, 'Craig Johnson', 22, 'Russian Federation', 'Test Engineer', None, 'Certificate Program', 7, None), (311, 'Kevin Bailey', 56, 'Benin', 'Professor', None, 'Associate Degree', 3, 'German B1'), (312, 'Leonard Hill', 34, 'Central African Republic', 'Cybersecurity Specialist', None, 'M Tech', 4, None), (313, 'Daryl Watson', 29, 'Slovakia (Slovak Republic)', 'UX/UI Designer', 'User Research, Wireframing, Usability Testing', None, 18, None), (314, 'Troy Baker', 26, 'Puerto Rico', 'Dancer', None, 'M Tech', 2, 'French Intermediate'), (315, 'Matthew Martin', 43, 'Taiwan', 'Test Engineer', None, 'B Tech', 7, 'Spanish Fluent'), (316, 'Katrina Evans', 22, 'Comoros', 'Athelete', None, 'Certificate Program', 7, 'Chinese Basic'), (317, 'Shannon Chang', 28, 'Saint Lucia', 'Cricketer', None, 'Associate Degree', 17, 'English Intermediate'), (318, 'Jesus Stewart', 29, 'Vanuatu', 'UX/UI Designer', None, \"Bachelor's Degree\", 19, 'German A2'), (319, 'Andrew Powell', 44, 'Australia', 'Singer', None, 'B Tech', 12, None), (320, 'Heather Graham', 36, 'Dominica', 'Database Administrator', None, 'B Tech', 12, 'German B2'), (321, 'Laura Adams', 52, 'China', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', None, 9, 'Chinese Basic'), (322, 'Melissa Waller', 27, 'Falkland Islands (Malvinas)', 'DevOps Engineer', None, 'High School', 3, None), (323, 'Edward Mccoy', 27, 'Cameroon', 'Truck Driver', 'Tanker, Hazmat, OTR', None, 9, 'Spanish Basic'), (324, 'Amy Myers', 38, 'Nigeria', 'School Bus Driver', None, 'BSc Nursing', 6, 'Chinese Basic'), (325, 'Kelly Odonnell', 30, 'Antigua and Barbuda', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'B Tech', 15, None), (326, 'Robert Robertson', 34, 'French Southern Territories', 'Engineer', 'Mechanical Engineering, Civil Engineering, Electrical Engineering', 'M Tech', 2, None), (327, 'Phillip Ewing', 30, 'Spain', 'Data Scientist', 'Statistics, Data Visualization, Machine Learning', None, 9, 'Spanish Intermediate'), (328, 'Shelby Butler', 22, 'Indonesia', 'Test Engineer', None, None, 12, None), (329, 'Carla Haney', 23, 'Lesotho', 'Database Administrator', 'Database Design, Backup and Recovery', \"Bachelor's Degree\", 5, 'Spanish Intermediate'), (330, 'Tina Martin', 35, 'Germany', 'Truck Driver', 'Defensive Driving, Hazmat, Tanker', 'Certificate Program', 17, 'English Intermediate'), (331, 'Margaret Alexander', 60, 'Andorra', 'Cybersecurity Specialist', 'Compliance, Threat Analysis', 'Associate Degree', 14, 'German A1'), (332, 'Julie Lindsey', 30, 'Montenegro', 'DevOps Engineer', 'Monitoring, CI/CD', 'Commercial License', 8, 'German A1'), (333, 'Robert White', 58, 'Montenegro', 'Database Administrator', 'Backup and Recovery, Database Design', 'PHD', 11, 'French Basic'), (334, 'Ryan Cameron', 39, 'Brunei Darussalam', 'Pilot', None, 'High School', 10, 'Spanish Basic'), (335, 'Margaret Miller', 39, 'Burkina Faso', 'Painter', None, 'M Tech', 11, 'Chinese Basic'), (336, 'Benjamin Howard', 51, 'Montenegro', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', None, 1, 'Chinese Basic'), (337, 'Michelle Ferrell', 24, 'Tajikistan', 'Office Driver', None, 'Commercial License', 20, 'Spanish Basic'), (338, 'Amber Watson', 31, 'French Southern Territories', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'M Tech', 20, 'German B2'), (339, 'Anne Simpson', 33, 'Tuvalu', 'Pilot', None, 'Associate Degree', 9, 'German C1'), (340, 'Melissa Rubio', 39, 'Russian Federation', 'Office Driver', None, None, 7, 'Chinese Basic'), (341, 'Robin Villarreal', 26, 'American Samoa', 'DevOps Engineer', 'Cloud Computing', 'Diploma', 20, 'Chinese Fluent'), (342, 'Charles Duran', 51, 'Macao', 'Network Administrator', 'Network Design, Network Security', 'High School', 1, 'Chinese Fluent'), (343, 'Heather Booth', 51, 'Denmark', 'Pilot', None, \"Bachelor's Degree\", 13, 'German B1'), (344, 'Lori Morrison', 23, 'Ireland', 'Engineer', None, None, 17, None), (345, 'Taylor Figueroa', 30, 'Chile', 'DevOps Engineer', None, None, 12, 'Spanish Intermediate'), (346, 'Christopher Baker', 40, 'Palau', 'Software Developer', None, \"Bachelor's Degree\", 12, 'English Fluent'), (347, 'Billy Mccormick', 57, \"Lao People's Democratic Republic\", 'UX/UI Designer', 'User Research, Wireframing', None, 17, 'French Basic'), (348, 'Michele Orozco', 31, 'Palau', 'Gym Trainer', None, 'BSc Nursing', 8, 'Spanish Intermediate'), (349, 'Krystal Meyer', 35, 'Kiribati', 'Cybersecurity Specialist', None, \"Master's Degree\", 16, None), (350, 'Vincent Lee', 32, 'Ethiopia', 'Office Driver', None, \"Bachelor's Degree\", 4, 'Spanish Intermediate'), (351, 'Tara Jones', 33, 'Saint Martin', 'Office Driver', None, None, 6, None), (352, 'Elizabeth Gonzales MD', 29, 'Namibia', 'Database Administrator', 'Database Design, Performance Tuning, Backup and Recovery', 'High School', 16, 'English Fluent'), (353, 'Katelyn Vega', 38, 'Brazil', 'Software Developer', 'Python, C++, Java, Agile Development, JavaScript', 'High School', 11, 'German C1'), (354, 'Sherri York', 23, 'Western Sahara', 'Test Engineer', None, \"Master's Degree\", 6, 'French Fluent'), (355, 'Jeff Bradley', 58, 'Honduras', 'Software Developer', None, 'M Tech', 14, 'Chinese Basic'), (356, 'Joshua Brown', 34, 'Turks and Caicos Islands', 'Pilot', None, 'Certificate Program', 16, 'French Intermediate'), (357, 'Kenneth Macias', 40, 'Mayotte', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'BSc Nursing', 20, 'English Basic'), (358, 'Cassandra Williams MD', 35, 'Belarus', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 12, 'German C1'), (359, 'Ashley Erickson', 39, 'Nigeria', 'Dancer', None, None, 9, 'Chinese Basic'), (360, 'Lee Molina', 58, 'Russian Federation', 'Office Driver', None, 'Diploma', 12, 'English Intermediate'), (361, 'Gary Vasquez', 42, 'Togo', 'Engineer', None, 'M Tech', 7, 'Spanish Fluent'), (362, 'Victoria Goodman', 42, 'Mayotte', 'Data Scientist', 'Statistics, Data Visualization', 'BSc Nursing', 6, None), (363, 'Karen Roberts', 26, 'Bangladesh', 'Athelete', None, 'PHD', 11, 'Spanish Intermediate'), (364, 'James Stevens', 31, 'Slovakia (Slovak Republic)', 'Pilot', None, None, 12, 'French Fluent'), (365, 'Timothy Fuentes', 54, 'Nigeria', 'Test Engineer', None, 'High School', 15, 'Chinese Fluent'), (366, 'Christopher Thompson', 60, 'Ukraine', 'Engineer', None, 'Certificate Program', 8, 'Chinese Fluent'), (367, 'Daniel Holloway', 28, 'Seychelles', 'Truck Driver', 'Tanker, Hazmat, OTR, Defensive Driving', 'M Tech', 13, 'Spanish Basic'), (368, 'Adam Hill', 52, 'Guernsey', 'Product Manager', None, 'B Tech', 6, None), (369, 'William Lewis', 52, 'Kenya', 'Software Developer', None, 'PHD', 20, None), (370, 'Jonathan Flowers', 50, 'Antarctica (the territory South of 60 deg S)', 'Cricketer', None, None, 1, 'English Fluent'), (371, 'Terry Lee', 44, 'New Zealand', 'Test Engineer', None, 'B Tech', 8, 'Spanish Basic'), (372, 'Miranda Chaney', 48, 'Slovenia', 'Pilot', None, 'M Tech', 18, 'Spanish Intermediate'), (373, 'Mark Ford', 41, 'Cayman Islands', 'Athelete', None, 'High School', 13, 'German A2'), (374, 'Gregory Brown', 27, 'Liberia', 'AI Engineer', None, 'PHD', 10, 'Chinese Basic'), (375, 'Erica Cohen', 58, 'Kiribati', 'Network Administrator', None, 'BSc Nursing', 18, 'Chinese Intermediate'), (376, 'Mrs. Danielle Williams', 47, 'Azerbaijan', 'Network Administrator', None, 'High School', 19, 'German C2'), (377, 'Dawn Hurley', 53, 'South Africa', 'DevOps Engineer', None, 'Commercial License', 2, 'English Intermediate'), (378, 'Mrs. Michelle Thompson', 36, 'United States Minor Outlying Islands', 'Licensed Practical Nurse', None, \"Bachelor's Degree\", 5, None), (379, 'Leslie Fields', 53, 'Ukraine', 'Product Manager', 'Market Research, Project Management, Product Development', 'PHD', 8, None), (380, 'Sandra Webb', 50, 'Croatia', 'Database Administrator', 'Database Design, Performance Tuning', 'Diploma', 13, None), (381, 'Angela Burns', 54, 'Botswana', 'Athelete', None, 'B Tech', 1, 'German C2'), (382, 'Christian Evans', 29, 'Ecuador', 'Athelete', None, 'M Tech', 8, 'German B2'), (383, 'April Rasmussen', 45, 'Zambia', 'Dancer', None, 'High School', 12, 'English Intermediate'), (384, 'Douglas Blackburn', 44, 'Niue', 'Database Administrator', None, None, 1, 'Chinese Fluent'), (385, 'Richard Hernandez', 33, 'Isle of Man', 'Singer', None, 'M Tech', 20, 'French Fluent'), (386, 'Amy Miller', 40, 'Oman', 'Licensed Practical Nurse', None, 'PHD', 20, None), (387, 'Charles Garcia', 49, 'Syrian Arab Republic', 'Office Driver', None, None, 16, None), (388, 'Tara Marks', 56, 'Antigua and Barbuda', 'Movie Director', None, \"Bachelor's Degree\", 2, 'French Basic'), (389, 'John Calhoun', 35, 'Cocos (Keeling) Islands', 'Data Scientist', 'Data Visualization, Machine Learning, Statistics', None, 7, 'German C1'), (390, 'Kevin Brown', 36, 'Eritrea', 'Dancer', None, \"Bachelor's Degree\", 8, 'English Basic'), (391, 'Joshua Vang', 53, 'Georgia', 'Painter', None, 'Certificate Program', 8, None), (392, 'Brooke Nixon', 24, 'Canada', 'Cricketer', None, 'PHD', 20, 'Spanish Intermediate'), (393, 'Sarah Evans', 33, 'Australia', 'Database Administrator', 'Backup and Recovery, Database Design, Performance Tuning', 'PHD', 12, None), (394, 'Jacqueline Howard', 59, 'Iraq', 'Social Media Influencer', None, 'B Tech', 13, 'French Basic'), (395, 'James James', 28, 'Mayotte', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Diploma', 17, 'English Basic'), (396, 'Kristin Park', 49, 'Oman', 'Dancer', None, 'High School', 15, 'English Intermediate'), (397, 'Lisa Pacheco', 51, 'Luxembourg', 'Athelete', None, 'Certificate Program', 9, 'French Intermediate'), (398, 'Linda Higgins', 24, 'Niue', 'AI Engineer', 'Computer Vision, Robotics, Natural Language Processing, Deep Learning', 'Certificate Program', 16, 'German B1'), (399, 'Corey Yang', 44, 'Netherlands Antilles', 'Nurse', 'Pediatrics, Surgery', 'Associate Degree', 6, 'Chinese Intermediate'), (400, 'Carolyn Black', 53, 'Myanmar', 'Nurse', None, 'High School', 20, None), (401, 'Dana Luna', 54, 'Taiwan', 'Engineer', 'Chemical Engineering, Civil Engineering', 'Commercial License', 19, None), (402, 'Jacob Mendez', 25, 'Bosnia and Herzegovina', 'Test Engineer', None, None, 17, 'Spanish Basic'), (403, 'James Coleman', 58, 'Sao Tome and Principe', 'Social Media Influencer', None, 'M Tech', 9, 'German B1'), (404, 'Gary Garcia', 40, 'Niue', 'School Bus Driver', 'Defensive Driving', 'Certificate Program', 12, 'Chinese Intermediate'), (405, 'Danny Arnold', 25, 'British Virgin Islands', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'M Tech', 19, 'French Fluent'), (406, 'Michael Young', 58, 'Slovakia (Slovak Republic)', 'Network Administrator', 'Network Security, Network Design', 'Diploma', 13, 'French Basic'), (407, 'Amanda Thomas', 46, 'North Macedonia', 'Test Engineer', None, 'Associate Degree', 11, 'Spanish Intermediate'), (408, 'Rodney Jones', 39, 'Bermuda', 'Athelete', None, 'PHD', 4, None), (409, 'Jose Jones', 47, 'Gabon', 'Social Media Influencer', None, 'B Tech', 6, 'German A2'), (410, 'Angela Herrera', 37, 'Svalbard & Jan Mayen Islands', 'Painter', None, 'M Tech', 1, 'English Fluent'), (411, 'Angela Sanchez', 49, 'Niue', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', 'Associate Degree', 7, 'German C1'), (412, 'Terri Smith', 48, 'Ghana', 'Movie Director', None, 'Commercial License', 20, 'Spanish Intermediate'), (413, 'Jessica Bell', 40, 'Luxembourg', 'Product Manager', 'Project Management, Product Development, Market Research', 'M Tech', 18, 'Spanish Basic'), (414, 'Thomas Mcdonald', 51, 'Congo', 'Athelete', None, 'Commercial License', 16, 'German B2'), (415, 'Brent Durham', 54, 'Burundi', 'AI Engineer', 'Natural Language Processing, Computer Vision', 'B Tech', 11, 'French Intermediate'), (416, 'Joanna Greene', 26, 'Afghanistan', 'Test Engineer', None, 'Certificate Program', 8, None), (417, 'Keith Bauer', 51, 'French Guiana', 'Engineer', None, 'M Tech', 5, 'Chinese Intermediate'), (418, 'Jessica Crosby', 39, 'Bahamas', 'Cybersecurity Specialist', None, 'PHD', 10, 'Chinese Basic'), (419, 'Stacey Ramirez', 37, 'Liechtenstein', 'Product Manager', 'Product Development, Market Research', 'High School', 18, 'Spanish Fluent'), (420, 'Jennifer Perez', 60, 'Sri Lanka', 'Cybersecurity Specialist', None, 'High School', 6, None), (421, 'Bradley Garcia', 41, 'Malawi', 'Pilot', None, None, 19, None), (422, 'Matthew Ross', 26, 'Russian Federation', 'Gym Trainer', None, 'Commercial License', 14, 'English Fluent'), (423, 'Nancy Franklin', 58, 'Algeria', 'Office Driver', None, \"Bachelor's Degree\", 12, 'French Basic'), (424, 'Jose Bradley', 40, 'Bahrain', 'Social Media Influencer', None, None, 20, None), (425, 'Ryan Avila', 47, 'Samoa', 'Engineer', None, None, 12, 'Chinese Fluent'), (426, 'Steven Ho', 28, 'Serbia', 'Professor', None, 'Diploma', 6, 'Spanish Intermediate'), (427, 'Jennifer Trevino', 47, 'Mali', 'Singer', None, 'B Tech', 9, None), (428, 'Amy Burns', 32, 'Botswana', 'Data Scientist', 'Data Visualization', 'PHD', 2, 'Spanish Fluent'), (429, 'Ashley Smith', 57, 'Saint Kitts and Nevis', 'Truck Driver', 'Tanker, OTR', 'Diploma', 14, 'German C1'), (430, 'Melissa Jones', 23, 'Somalia', 'Movie Producer', None, 'Associate Degree', 16, None), (431, 'Casey Flores', 31, 'Fiji', 'Movie Director', None, 'PHD', 11, 'French Basic'), (432, 'Jonathan Robertson', 36, 'Argentina', 'Teacher', None, 'Diploma', 15, 'French Intermediate'), (433, 'Natalie Torres', 25, 'Aruba', 'Product Manager', None, \"Master's Degree\", 5, None), (434, 'Tyler Hall', 31, 'Maldives', 'Nurse', 'ICU, Long-Term Care, Wound Care, Emergency, Surgery', 'M Tech', 3, 'German C2'), (435, 'Michelle Mccoy', 25, 'Marshall Islands', 'Pilot', None, None, 19, None), (436, 'Ashley White', 39, 'Comoros', 'Painter', None, 'Associate Degree', 20, 'French Basic'), (437, 'Tyler Weeks', 56, 'Iceland', 'UX/UI Designer', 'Usability Testing, Prototyping, User Research, Wireframing', None, 16, 'German B2'), (438, 'Ryan Walker', 58, 'Iran', 'Singer', None, 'Associate Degree', 18, 'English Fluent'), (439, 'Mrs. Samantha Hicks', 54, 'American Samoa', 'Social Media Influencer', None, \"Master's Degree\", 20, 'English Fluent'), (440, 'Pamela Smith', 24, 'Anguilla', 'Software Developer', 'JavaScript, C++, Agile Development, Python, Java', 'High School', 10, 'Chinese Fluent'), (441, 'Karen Anderson', 51, 'Zimbabwe', 'Cricketer', None, 'M Tech', 5, 'English Intermediate'), (442, 'Claudia Ward', 44, 'Papua New Guinea', 'Office Driver', None, 'Commercial License', 7, 'English Basic'), (443, 'David Hunter', 24, 'Hungary', 'Engineer', 'Mechanical Engineering, Civil Engineering, Chemical Engineering, Electrical Engineering', 'Associate Degree', 2, 'Chinese Intermediate'), (444, 'Chelsea Ross', 51, 'Northern Mariana Islands', 'Licensed Practical Nurse', 'Long-Term Care', None, 13, 'French Basic'), (445, 'Daniel Gill', 47, 'Greenland', 'Movie Producer', None, 'High School', 3, 'Chinese Fluent'), (446, 'Diana Williams', 23, 'Cyprus', 'Engineer', 'Chemical Engineering', \"Bachelor's Degree\", 14, None), (447, 'Tammy Santana', 52, 'Saint Helena', 'Cybersecurity Specialist', 'Incident Response, Network Security, Compliance, Threat Analysis', 'M Tech', 9, None), (448, 'Jerry Randolph', 28, 'Wallis and Futuna', 'Software Developer', 'Python, JavaScript, Java, C++', None, 14, None), (449, 'Steve Weiss', 45, 'Sierra Leone', 'Movie Director', None, 'M Tech', 3, 'Spanish Intermediate'), (450, 'Amanda Jones', 56, 'Liberia', 'Movie Director', None, 'BSc Nursing', 11, 'English Fluent'), (451, 'Patrick Thompson', 44, 'Papua New Guinea', 'Software Developer', 'C++, Agile Development', 'BSc Nursing', 15, 'English Basic'), (452, 'Stephen Robertson', 34, 'Reunion', 'Teacher', None, 'BSc Nursing', 11, 'French Fluent'), (453, 'Kimberly Hansen', 58, 'French Guiana', 'AI Engineer', None, 'PHD', 6, 'German B2'), (454, 'Jeremy Reynolds', 51, 'Georgia', 'Product Manager', 'Product Development, Project Management', \"Master's Degree\", 2, None), (455, 'Mr. John Reed', 36, 'Korea', 'Data Scientist', None, 'Diploma', 19, 'English Fluent'), (456, 'David Allen', 55, 'Solomon Islands', 'Teacher', None, 'B Tech', 5, 'English Intermediate'), (457, 'Jonathan Dennis', 48, 'Haiti', 'Network Administrator', 'Network Design', 'BSc Nursing', 6, 'French Fluent'), (458, 'Jonathan Ortega', 51, 'Ghana', 'DevOps Engineer', None, \"Bachelor's Degree\", 1, 'French Basic'), (459, 'Joseph Collins', 51, 'Tanzania', 'Product Manager', None, \"Bachelor's Degree\", 1, 'Chinese Basic'), (460, 'Amy Buchanan', 51, 'Vanuatu', 'School Bus Driver', None, 'Commercial License', 3, None), (461, 'Trevor Anderson', 28, 'Jamaica', 'Gym Trainer', None, 'PHD', 3, 'Chinese Fluent'), (462, 'Joshua Roman', 50, 'Eritrea', 'Truck Driver', 'Defensive Driving, OTR, Hazmat', 'Diploma', 20, 'Spanish Fluent'), (463, 'Stephanie Shah', 57, 'New Zealand', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis, Network Security', 'B Tech', 5, 'French Basic'), (464, 'Rachel Carr', 26, 'Guam', 'Network Administrator', 'Network Design', None, 1, 'Spanish Intermediate'), (465, 'Daniel Ford', 41, 'South Africa', 'AI Engineer', None, None, 10, 'Spanish Fluent'), (466, 'William Wright', 49, 'Kuwait', 'Social Media Influencer', None, 'PHD', 16, None), (467, 'George Patterson', 50, 'Guyana', 'Professor', None, None, 20, None), (468, 'Jonathan Thomas', 29, 'Sri Lanka', 'Gym Trainer', None, 'Commercial License', 11, 'Chinese Intermediate'), (469, 'Miss Sheila Brewer', 44, 'Burkina Faso', 'Software Developer', 'JavaScript, Java', 'Commercial License', 13, 'Chinese Basic'), (470, 'Joy Castillo', 29, 'Austria', 'Test Engineer', None, 'PHD', 1, 'French Basic'), (471, 'Patrick Smith', 25, 'Mozambique', 'Network Administrator', 'Network Security, Network Design, Troubleshooting', None, 7, 'English Fluent'), (472, 'Crystal Thompson', 41, 'Portugal', 'Nurse', None, 'Diploma', 14, None), (473, 'James Martinez', 26, 'Brunei Darussalam', 'Painter', None, 'Diploma', 13, None), (474, 'Jillian Little', 45, 'Botswana', 'Truck Driver', 'Tanker, Defensive Driving, Hazmat', 'Certificate Program', 18, 'French Fluent'), (475, 'Timothy Johnson', 25, 'Vanuatu', 'Gym Trainer', None, 'Certificate Program', 6, 'English Fluent'), (476, 'Robert Brown Jr.', 41, 'Solomon Islands', 'Painter', None, 'B Tech', 18, 'Chinese Fluent'), (477, 'Brenda Calhoun', 51, 'Bolivia', 'Pilot', None, \"Master's Degree\", 5, 'German C2'), (478, 'Timothy Dyer', 22, 'Bangladesh', 'Painter', None, None, 4, 'German A2'), (479, 'Crystal Andrews', 46, 'Gambia', 'UX/UI Designer', 'Prototyping, Wireframing, User Research', 'B Tech', 12, 'German A1'), (480, 'William Cain', 48, 'British Indian Ocean Territory (Chagos Archipelago)', 'Office Driver', None, None, 20, 'German C1'), (481, 'Stuart Brooks', 27, 'Turkey', 'Database Administrator', 'Database Design, Performance Tuning', \"Bachelor's Degree\", 16, 'Spanish Intermediate'), (482, 'Kellie Garcia', 44, 'Monaco', 'Singer', None, 'B Tech', 20, 'Chinese Intermediate'), (483, 'Joseph Phillips', 32, 'Qatar', 'Network Administrator', 'Network Design, Network Security', 'BSc Nursing', 4, 'Chinese Basic'), (484, 'Wendy Smith', 45, 'Mongolia', 'Cricketer', None, 'PHD', 1, 'English Fluent'), (485, 'Maria Burns', 24, 'Papua New Guinea', 'Truck Driver', 'OTR', None, 9, 'Chinese Fluent'), (486, 'Robert Powell', 43, 'Bulgaria', 'Teacher', None, 'B Tech', 20, 'French Basic'), (487, 'Anna Flowers', 42, 'Croatia', 'Movie Director', None, \"Master's Degree\", 15, 'Chinese Basic'), (488, 'Pamela Taylor', 44, 'China', 'Singer', None, 'PHD', 19, 'Spanish Intermediate'), (489, 'Lindsay Jenkins', 55, 'Jamaica', 'Truck Driver', 'Tanker, Hazmat', 'B Tech', 17, 'French Fluent'), (490, 'Robert Stevenson', 51, 'India', 'Dancer', None, \"Bachelor's Degree\", 19, None), (491, 'Jason Knight', 44, 'Portugal', 'Data Scientist', 'Data Analysis', \"Master's Degree\", 6, 'French Intermediate'), (492, 'Matthew Miller', 47, 'Bosnia and Herzegovina', 'Singer', None, 'Associate Degree', 10, 'Chinese Fluent'), (493, 'David Harrell', 47, 'Canada', 'Professor', None, 'Diploma', 16, 'French Intermediate'), (494, 'Jonathan Mcgrath', 41, 'Kuwait', 'School Bus Driver', 'Passenger Endorsement', 'Commercial License', 11, 'Spanish Fluent'), (495, 'Alison Johnson', 27, 'Russian Federation', 'Singer', None, None, 15, None), (496, 'Kimberly Williams', 33, 'Eritrea', 'Athelete', None, \"Master's Degree\", 17, None), (497, 'Katie Green', 46, 'Belize', 'Office Driver', None, None, 1, 'Chinese Basic'), (498, 'Dean Gallegos', 46, 'Switzerland', 'DevOps Engineer', 'CI/CD', 'BSc Nursing', 15, 'Spanish Basic'), (499, 'James Wade', 26, 'Albania', 'Database Administrator', None, None, 20, None), (500, 'Robert Murphy', 39, 'Kiribati', 'Social Media Influencer', None, None, 10, 'English Basic')]\n"]}], "source": ["query_result = db.run(\"SELECT * FROM candidates ;\")\n", "print(\"Query result from Employees candidates: \\n\", query_result)"]}, {"cell_type": "code", "execution_count": 14, "id": "26307c0a", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "llm=ChatOpenAI(\n", "    model=\"gpt-4.1\",\n", "    temperature=0.1,\n", "    max_tokens=1000,\n", "    verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "44478317", "metadata": {}, "outputs": [], "source": ["from langchain_community.agent_toolkits import SQLDatabaseToolkit"]}, {"cell_type": "code", "execution_count": 16, "id": "e8e53184", "metadata": {}, "outputs": [], "source": ["toolkit=SQLDatabaseToolkit(db=db,llm=llm)"]}, {"cell_type": "code", "execution_count": 17, "id": "38a2fd0d", "metadata": {}, "outputs": [], "source": ["tools=toolkit.get_tools()"]}, {"cell_type": "code", "execution_count": 18, "id": "a527f5b0", "metadata": {}, "outputs": [{"data": {"text/plain": ["[QuerySQLDatabaseTool(description=\"Input to this tool is a detailed and correct SQL query, output is a result from the database. If the query is not correct, an error message will be returned. If an error is returned, rewrite the query, check the query, and try again. If you encounter an issue with Unknown column 'xxxx' in 'field list', use sql_db_schema to query the correct table fields.\", db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>),\n", " InfoSQLDatabaseTool(description='Input to this tool is a comma-separated list of tables, output is the schema and sample rows for those tables. Be sure that the tables actually exist by calling sql_db_list_tables first! Example Input: table1, table2, table3', db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>),\n", " ListSQLDatabaseTool(db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>),\n", " QuerySQLCheckerTool(description='Use this tool to double check if your query is correct before executing it. Always use this tool before executing a query with sql_db_query!', db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>, llm=ChatOpenAI(client=<openai.resources.chat.completions.completions.Completions object at 0x711aa3140320>, async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x711aa3141eb0>, root_client=<openai.OpenAI object at 0x711aaa666c30>, root_async_client=<openai.AsyncOpenAI object at 0x711aa3140350>, model_name='gpt-4.1', temperature=0.1, model_kwargs={}, openai_api_key=SecretStr('**********'), max_tokens=1000), llm_chain=LLMChain(verbose=False, prompt=PromptTemplate(input_variables=['dialect', 'query'], input_types={}, partial_variables={}, template='\\n{query}\\nDouble check the {dialect} query above for common mistakes, including:\\n- Using NOT IN with NULL values\\n- Using UNION when UNION ALL should have been used\\n- Using BETWEEN for exclusive ranges\\n- Data type mismatch in predicates\\n- Properly quoting identifiers\\n- Using the correct number of arguments for functions\\n- Casting to the correct data type\\n- Using the proper columns for joins\\n\\nIf there are any of the above mistakes, rewrite the query. If there are no mistakes, just reproduce the original query.\\n\\nOutput the final SQL query only.\\n\\nSQL Query: '), llm=ChatOpenAI(client=<openai.resources.chat.completions.completions.Completions object at 0x711aa3140320>, async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x711aa3141eb0>, root_client=<openai.OpenAI object at 0x711aaa666c30>, root_async_client=<openai.AsyncOpenAI object at 0x711aa3140350>, model_name='gpt-4.1', temperature=0.1, model_kwargs={}, openai_api_key=SecretStr('**********'), max_tokens=1000), output_parser=StrOutputParser(), llm_kwargs={}))]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["tools"]}, {"cell_type": "code", "execution_count": 19, "id": "b6c033d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sql_db_query\n", "sql_db_schema\n", "sql_db_list_tables\n", "sql_db_query_checker\n"]}], "source": ["for tool in tools:\n", "    print(tool.name)"]}, {"cell_type": "code", "execution_count": 20, "id": "cc3e5a40", "metadata": {}, "outputs": [], "source": ["list_tables_tool = next((tool for tool in tools if tool.name == \"sql_db_list_tables\"), None)"]}, {"cell_type": "code", "execution_count": 21, "id": "03c44003", "metadata": {}, "outputs": [{"data": {"text/plain": ["ListSQLDatabaseTool(db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["list_tables_tool"]}, {"cell_type": "code", "execution_count": 22, "id": "8e17f6fb", "metadata": {}, "outputs": [], "source": ["get_schema_tool = next((tool for tool in tools if tool.name == \"sql_db_schema\"), None)"]}, {"cell_type": "code", "execution_count": 24, "id": "c025c879", "metadata": {}, "outputs": [{"data": {"text/plain": ["InfoSQLDatabaseTool(description='Input to this tool is a comma-separated list of tables, output is the schema and sample rows for those tables. Be sure that the tables actually exist by calling sql_db_list_tables first! Example Input: table1, table2, table3', db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x711aa8ff9550>)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["get_schema_tool"]}, {"cell_type": "code", "execution_count": 25, "id": "3abc5c17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["candidates, sqlite_sequence\n"]}], "source": ["print(list_tables_tool.invoke(\"\"))\n"]}, {"cell_type": "code", "execution_count": 31, "id": "e8129dc4", "metadata": {}, "outputs": [], "source": ["query_result = db.run(\"SELECT * FROM candidates where experience_years >5 ;\")\n", "# print(\"Query result from Employees candidates: \\n\", query_result)"]}, {"cell_type": "markdown", "id": "5d46fd60", "metadata": {}, "source": ["### langchain sql"]}, {"cell_type": "code", "execution_count": 1, "id": "b6341c60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dialect: sqlite\n", "Usable tables: ['candidates', 'sqlite_sequence']\n"]}], "source": ["from langchain_community.utilities import SQLDatabase\n", "import pandas as pd\n", "db = SQLDatabase.from_uri(\"sqlite:///databases/candidates_details_500.db\")\n", "\n", "print(f\"Dialect: {db.dialect}\")\n", "print(f\"Usable tables: {db.get_usable_table_names()}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "aac048bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query result from candidates table: \n", " [(1, '<PERSON><PERSON><PERSON>', 47, '<PERSON>', 'AI Engineer', 'Robotics, Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 5, 'French Basic'), (2, '<PERSON>', 56, 'Portugal', 'Office Driver', None, 'Certificate Program', 9, 'French Fluent'), (3, '<PERSON>', 43, 'Swaziland', 'Office Driver', <PERSON>, 'B Tech', 1, 'English Intermediate'), (4, '<PERSON>', 25, 'Tunisia', 'Software Developer', 'C++, Python, JavaScript', 'BSc Nursing', 2, 'Chinese Fluent'), (5, '<PERSON>', 60, 'Tunisia', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'High School', 10, 'German B1'), (6, '<PERSON>', 30, 'Sri Lanka', 'Office Driver', None, None, 8, 'Spanish Intermediate'), (7, '<PERSON>', 49, 'Bosnia and Herzegovina', '<PERSON>', None, \"Master's Degree\", 19, None), (8, '<PERSON>', 31, '<PERSON>', 'Cybersecurity Specialist', 'Incident Response', \"Master's Degree\", 3, 'German C2'), (9, '<PERSON>', 32, '<PERSON><PERSON><PERSON>', '<PERSON>', <PERSON>, 'BSc Nursing', 5, None), (10, '<PERSON>', 37, '<PERSON>', '<PERSON><PERSON>', None, \"Bachelor's <PERSON>\", 9, 'English Fluent'), (11, '<PERSON> <PERSON><PERSON>', 53, 'Nigeria', 'Movie Director', <PERSON>, 'Associate Degree', 13, 'Spanish Basic'), (12, '<PERSON> <PERSON>', 43, '<PERSON>', 'Nurse', 'Wound Care, <PERSON>-Term Care, <PERSON>', \"Master's Degree\", 16, 'Chinese Basic'), (13, '<PERSON> <PERSON>', 51, '<PERSON>', '<PERSON> Scientist', '<PERSON> <PERSON>', \"Master's Degree\", 3, 'Spanish Intermediate'), (14, 'Daniel Harrington', 49, 'Dominican Republic', 'Teacher', None, 'High School', 5, 'Chinese Basic'), (15, 'Amy Alexander', 50, 'Guam', 'Movie Director', None, 'BSc Nursing', 10, 'Chinese Basic'), (16, 'Keith Krueger', 56, 'Afghanistan', 'Test Engineer', None, None, 15, 'Spanish Intermediate'), (17, 'William Thompson', 47, 'Andorra', 'Movie Director', None, 'PHD', 14, None), (18, 'Diana Foster', 42, 'Bosnia and Herzegovina', 'UX/UI Designer', 'Wireframing', 'Certificate Program', 5, None), (19, 'Martin Bright', 56, 'Pakistan', 'Engineer', None, None, 5, 'German B2'), (20, 'Alicia Webster', 39, 'Russian Federation', 'Cricketer', None, 'Commercial License', 3, None), (21, 'Heather Gardner', 54, 'Saint Martin', 'Nurse', 'ICU, Pediatrics, Long-Term Care, Emergency, Surgery', 'High School', 9, 'French Fluent'), (22, 'Derek Hardy', 34, 'Grenada', 'Professor', None, 'B Tech', 12, 'Spanish Intermediate'), (23, 'Laura Thomas', 51, 'Jamaica', 'Singer', None, 'M Tech', 8, None), (24, 'Carrie Hill', 59, 'Somalia', 'Software Developer', None, 'M Tech', 7, 'Chinese Intermediate'), (25, 'Brooke Pham', 48, 'Svalbard & Jan Mayen Islands', 'Teacher', None, \"Master's Degree\", 10, 'Spanish Basic'), (26, 'Cynthia Maldonado', 57, 'Ecuador', 'Movie Director', None, 'M Tech', 15, None), (27, 'Julia Rangel', 58, 'Bhutan', 'Database Administrator', None, 'Certificate Program', 1, None), (28, 'Jeffrey Dawson', 27, 'Dominican Republic', 'Gym Trainer', None, None, 17, 'English Fluent'), (29, 'Jacob Reynolds', 44, 'Sierra Leone', 'Gym Trainer', None, None, 19, 'English Basic'), (30, 'Ruth Sullivan', 54, 'Sri Lanka', 'Movie Director', None, None, 19, 'English Intermediate'), (31, 'Cynthia Howell', 54, 'Aruba', 'Movie Director', None, 'Commercial License', 2, 'French Intermediate'), (32, 'Samantha Clark', 54, 'Saint Helena', 'Athelete', None, 'M Tech', 20, 'English Intermediate'), (33, 'Jeffery Gilbert', 34, 'Svalbard & Jan Mayen Islands', 'UX/UI Designer', 'Prototyping, Wireframing', None, 18, 'French Fluent'), (34, 'Cindy Gibbs', 54, 'Chile', 'Cricketer', None, \"Bachelor's Degree\", 18, 'English Fluent'), (35, 'Elizabeth Smith DDS', 46, 'Pakistan', 'Licensed Practical Nurse', None, 'M Tech', 19, None), (36, 'Matthew Santos', 27, 'Bangladesh', 'Product Manager', 'Project Management, Market Research, Product Development', \"Master's Degree\", 15, 'Spanish Intermediate'), (37, 'Megan Snow', 26, 'Aruba', 'Cricketer', None, \"Master's Degree\", 10, 'German A2'), (38, 'Michelle Vargas', 44, 'Hong Kong', 'Movie Director', None, None, 7, 'Spanish Intermediate'), (39, 'Benjamin Cruz', 25, 'Russian Federation', 'Professor', None, 'Commercial License', 19, 'English Basic'), (40, 'Angela Ramsey', 44, 'Anguilla', 'Gym Trainer', None, 'Commercial License', 18, 'English Fluent'), (41, 'Robert Murillo', 39, 'New Caledonia', 'Singer', None, 'Certificate Program', 18, 'English Basic'), (42, 'Edward Boyd', 33, 'Madagascar', 'Movie Producer', None, 'Associate Degree', 10, 'German C1'), (43, 'Jamie Williams', 48, 'Pakistan', 'Professor', None, 'Diploma', 6, 'English Intermediate'), (44, 'Ann Everett', 25, 'Norfolk Island', 'Dancer', None, None, 12, 'Spanish Intermediate'), (45, 'Carolyn Franklin', 40, 'Greenland', 'Test Engineer', None, 'M Tech', 20, None), (46, 'Teresa Greene', 54, 'Saint Vincent and the Grenadines', 'School Bus Driver', None, 'BSc Nursing', 13, 'Spanish Intermediate'), (47, 'Heather White', 28, 'Gibraltar', 'Engineer', 'Civil Engineering, Chemical Engineering, Electrical Engineering, Mechanical Engineering', None, 11, 'Spanish Fluent'), (48, 'Chris Price', 22, 'Puerto Rico', 'Gym Trainer', None, \"Bachelor's Degree\", 17, 'Spanish Basic'), (49, 'Anne Long', 51, 'British Virgin Islands', 'Teacher', None, 'Diploma', 2, 'English Intermediate'), (50, 'John Bradford', 40, 'Lesotho', 'Test Engineer', None, 'B Tech', 19, 'French Intermediate'), (51, 'Hunter Miller', 22, 'Mexico', 'Dancer', None, \"Master's Degree\", 6, 'English Intermediate'), (52, 'Michelle Roberts', 51, 'Bermuda', 'Engineer', 'Civil Engineering, Chemical Engineering', None, 11, 'Spanish Intermediate'), (53, 'Kyle Richardson', 42, 'Ecuador', 'Dancer', None, 'Diploma', 11, 'French Basic'), (54, 'Hannah Smith', 57, 'Sao Tome and Principe', 'DevOps Engineer', 'Cloud Computing, Containerization', 'Commercial License', 10, None), (55, 'John Gonzalez', 52, 'Jersey', 'Gym Trainer', None, 'Commercial License', 12, 'Chinese Basic'), (56, 'Latoya Ramsey', 41, 'Libyan Arab Jamahiriya', 'Social Media Influencer', None, None, 9, 'German C2'), (57, 'Sarah Jones', 49, 'South Africa', 'Cricketer', None, 'BSc Nursing', 15, None), (58, 'Jill Phillips', 58, 'Cape Verde', 'Professor', None, \"Bachelor's Degree\", 16, 'Spanish Basic'), (59, 'Amy Rodriguez', 26, \"Lao People's Democratic Republic\", 'Social Media Influencer', None, \"Bachelor's Degree\", 4, 'French Fluent'), (60, 'Eugene Bonilla', 26, 'Mauritius', 'Office Driver', None, None, 6, None), (61, 'Jennifer Bass MD', 58, 'Northern Mariana Islands', 'Test Engineer', None, None, 19, None), (62, 'John Carey', 29, 'Ireland', 'Product Manager', 'Project Management, Product Development, Market Research', 'High School', 16, None), (63, 'Regina Mckenzie', 22, 'Hungary', 'Software Developer', None, 'BSc Nursing', 9, 'German B2'), (64, 'Terri Harris', 50, 'Chile', 'Pilot', None, 'B Tech', 18, None), (65, 'Daniel Hawkins', 32, 'Malta', 'Cybersecurity Specialist', None, None, 1, 'Chinese Intermediate'), (66, 'Sally Curry', 22, 'Georgia', 'Professor', None, None, 2, 'Spanish Intermediate'), (67, 'Suzanne Delgado', 52, 'Greenland', 'Database Administrator', 'Backup and Recovery', 'BSc Nursing', 2, 'English Basic'), (68, 'Tiffany Jones', 26, 'Western Sahara', 'Office Driver', None, 'BSc Nursing', 9, 'English Basic'), (69, 'Carolyn Mahoney', 25, 'Lithuania', 'Movie Producer', None, 'High School', 9, 'German B1'), (70, 'Kimberly Hobbs', 60, 'Saint Kitts and Nevis', 'Dancer', None, 'B Tech', 1, None), (71, 'Tristan Warren', 34, 'Mozambique', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'M Tech', 17, 'English Intermediate'), (72, 'Samantha Jacobson', 28, 'Guam', 'Data Scientist', 'Statistics, Machine Learning, Data Analysis, Data Visualization', 'M Tech', 16, 'Spanish Fluent'), (73, 'Heidi House', 58, 'United Kingdom', 'Nurse', 'Pediatrics, Surgery', 'BSc Nursing', 12, None), (74, 'Chelsea Collins', 36, 'Lesotho', 'Singer', None, 'PHD', 1, 'Chinese Fluent'), (75, 'Jason Moreno', 55, 'Togo', 'Database Administrator', 'Performance Tuning, Backup and Recovery, Database Design', None, 16, 'French Basic'), (76, 'Jack Reyes', 43, 'United Arab Emirates', 'Athelete', None, 'BSc Nursing', 17, 'French Fluent'), (77, 'Lisa Randall', 32, 'United States Minor Outlying Islands', 'Engineer', 'Electrical Engineering, Mechanical Engineering, Chemical Engineering', None, 5, 'German C2'), (78, 'James Wilkinson', 53, 'Cuba', 'Product Manager', 'Product Development, Market Research', 'PHD', 2, None), (79, 'Jeffrey Salazar', 51, 'Norway', 'Engineer', 'Mechanical Engineering, Chemical Engineering, Civil Engineering, Electrical Engineering', 'PHD', 9, 'Chinese Fluent'), (80, 'Heather Lynn', 47, 'Qatar', 'Pilot', None, None, 16, 'Chinese Basic'), (81, 'Meghan Martin', 47, 'Cambodia', 'Software Developer', 'JavaScript, C++, Python, Java, Agile Development', 'Diploma', 15, 'French Basic'), (82, 'William Foster', 60, 'Grenada', 'Software Developer', None, 'Diploma', 5, 'English Fluent'), (83, 'Reginald Williams', 59, 'Aruba', 'Dancer', None, 'B Tech', 17, 'German A1'), (84, 'John Ali', 39, 'Egypt', 'Painter', None, None, 15, 'German B2'), (85, 'Laura Holt', 30, 'Vietnam', 'Licensed Practical Nurse', 'Medication Administration', None, 11, 'Spanish Intermediate'), (86, 'Ashley Wolf', 51, 'Namibia', 'Gym Trainer', None, 'BSc Nursing', 10, 'Chinese Fluent'), (87, 'Jennifer Suarez', 44, 'Barbados', 'Singer', None, 'PHD', 10, 'Chinese Basic'), (88, 'Alexander Smith', 36, 'Haiti', 'Network Administrator', None, None, 3, None), (89, 'Ian Hoffman', 36, 'Tanzania', 'Pilot', None, 'BSc Nursing', 9, None), (90, 'Louis Hernandez', 25, 'Moldova', 'Social Media Influencer', None, 'Certificate Program', 11, None), (91, 'Terri Gray', 25, 'Saint Helena', 'AI Engineer', 'Computer Vision, Natural Language Processing, Deep Learning', 'B Tech', 11, None), (92, 'Diane Ramirez', 28, 'Singapore', 'Painter', None, \"Master's Degree\", 3, 'Chinese Basic'), (93, 'Megan Anderson', 27, 'Brazil', 'Cybersecurity Specialist', None, None, 13, 'Chinese Basic'), (94, 'Andre Olson', 49, 'Qatar', 'Cybersecurity Specialist', None, 'Certificate Program', 6, 'Chinese Intermediate'), (95, 'Ian Huang', 40, 'New Zealand', 'Product Manager', 'Project Management, Market Research', 'Commercial License', 8, 'German C2'), (96, 'Kelly Gonzalez', 56, 'Tokelau', 'Pilot', None, None, 5, 'German C2'), (97, 'Bruce Smith', 22, 'Grenada', 'Singer', None, 'Diploma', 6, 'Spanish Intermediate'), (98, 'Renee Cooper', 49, 'American Samoa', 'Test Engineer', None, \"Bachelor's Degree\", 4, 'Spanish Fluent'), (99, 'John Hall', 51, 'Holy See (Vatican City State)', 'Engineer', None, 'PHD', 19, 'English Basic'), (100, 'Jennifer Sullivan', 45, 'Trinidad and Tobago', 'Dancer', None, 'Diploma', 11, 'French Basic'), (101, 'Jeffrey Orozco', 60, 'Niger', 'Software Developer', 'Agile Development', 'M Tech', 7, 'English Intermediate'), (102, 'Timothy Green', 40, 'Malawi', 'Movie Producer', None, \"Master's Degree\", 17, None), (103, 'Nancy Davidson', 23, 'Suriname', 'Athelete', None, 'BSc Nursing', 15, 'French Fluent'), (104, 'Marcus Jones', 27, 'Faroe Islands', 'Product Manager', None, 'Commercial License', 19, 'Spanish Fluent'), (105, 'Cole Wright', 24, 'Timor-Leste', 'Professor', None, None, 18, None), (106, 'Mark Schaefer', 59, 'Czech Republic', 'Licensed Practical Nurse', None, \"Master's Degree\", 2, 'German C2'), (107, 'Amy Kelly', 46, 'United Kingdom', 'UX/UI Designer', 'Usability Testing, User Research', \"Bachelor's Degree\", 14, 'Chinese Intermediate'), (108, 'Amanda Thompson', 30, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, None, 16, 'German B1'), (109, 'Michelle Hines', 46, 'Iran', 'School Bus Driver', 'Defensive Driving', 'PHD', 5, 'French Intermediate'), (110, 'Ronald Barnett', 25, 'Cuba', 'Movie Director', None, 'BSc Nursing', 7, 'German B1'), (111, 'Peggy Garcia', 23, 'Kenya', 'Data Scientist', 'Statistics, Data Analysis, Machine Learning', 'Diploma', 18, 'Chinese Fluent'), (112, 'Mark Miller', 26, 'Iceland', 'Cybersecurity Specialist', 'Compliance, Incident Response, Threat Analysis', 'M Tech', 20, 'English Fluent'), (113, 'Katie Gonzalez', 49, 'Anguilla', 'Database Administrator', None, None, 12, 'Chinese Fluent'), (114, 'Lisa Beltran', 52, 'Antarctica (the territory South of 60 deg S)', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'PHD', 6, 'German A2'), (115, 'Janice Ross', 54, 'Vanuatu', 'UX/UI Designer', None, None, 6, 'Spanish Fluent'), (116, 'Jeremiah Moore', 43, 'Vietnam', 'Dancer', None, 'Commercial License', 8, 'English Fluent'), (117, 'Kaitlyn Wilson', 45, 'Hong Kong', 'Singer', None, None, 2, 'German A2'), (118, 'Margaret Patterson', 23, 'Djibouti', 'Singer', None, 'Commercial License', 3, 'German C2'), (119, 'Ethan Rice', 28, 'French Southern Territories', 'Gym Trainer', None, 'High School', 1, 'German C1'), (120, 'Jennifer Ritter', 24, 'Eritrea', 'AI Engineer', None, None, 17, None), (121, 'Paul Hubbard', 30, 'Italy', 'Cricketer', None, None, 1, 'French Fluent'), (122, 'Julie Patel', 38, 'Burundi', 'Dancer', None, \"Master's Degree\", 7, 'Chinese Basic'), (123, 'Craig Fisher', 43, 'New Zealand', 'UX/UI Designer', 'Prototyping, User Research', 'Certificate Program', 3, 'Chinese Intermediate'), (124, 'Andrea Ramsey', 58, 'Western Sahara', 'Software Developer', 'Python, Java, JavaScript', 'PHD', 13, 'English Fluent'), (125, 'Christian Wallace', 50, 'Ghana', 'Nurse', 'Pediatrics', \"Master's Degree\", 10, 'Chinese Fluent'), (126, 'Lauren Harrell', 32, 'Yemen', 'DevOps Engineer', 'Containerization, Cloud Computing', \"Bachelor's Degree\", 18, 'French Fluent'), (127, 'Vincent Hernandez', 34, 'Bahamas', 'Pilot', None, \"Bachelor's Degree\", 15, None), (128, 'Christina Brown', 33, 'Eritrea', 'Cricketer', None, 'PHD', 17, 'English Basic'), (129, 'Ronald Harris', 26, 'Isle of Man', 'Truck Driver', 'OTR, Tanker', 'BSc Nursing', 4, 'English Intermediate'), (130, 'Kelly Coleman', 44, 'Angola', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'Associate Degree', 4, 'Spanish Intermediate'), (131, 'Amanda Taylor', 31, 'Mozambique', 'Data Scientist', 'Statistics, Machine Learning', 'PHD', 13, 'English Basic'), (132, 'Mark Foster', 49, 'Andorra', 'Test Engineer', None, None, 6, None), (133, 'James Marsh', 43, 'Saint Pierre and Miquelon', 'AI Engineer', 'Deep Learning, Natural Language Processing, Robotics', 'B Tech', 10, 'Spanish Basic'), (134, 'Judith Dunn', 31, 'Tuvalu', 'Singer', None, 'High School', 11, 'English Basic'), (135, 'Michael Brown', 43, 'Estonia', 'Database Administrator', 'Database Design', \"Bachelor's Degree\", 7, 'Spanish Fluent'), (136, 'Vicki Smith', 25, 'Uganda', 'Software Developer', 'JavaScript', 'Commercial License', 11, 'German C2'), (137, 'Jason Soto', 50, 'Burundi', 'UX/UI Designer', 'Prototyping, Usability Testing', 'BSc Nursing', 16, 'French Fluent'), (138, 'Margaret Dougherty', 33, 'Romania', 'Network Administrator', 'Troubleshooting, Network Design', None, 6, 'German C1'), (139, 'James White', 49, 'Saint Martin', 'Professor', None, None, 10, 'Chinese Basic'), (140, 'Andrew Ortiz', 48, 'Central African Republic', 'AI Engineer', None, 'PHD', 10, 'Chinese Fluent'), (141, 'Crystal David DVM', 42, 'French Polynesia', 'UX/UI Designer', 'Usability Testing, User Research, Wireframing, Prototyping', 'Commercial License', 13, 'Spanish Fluent'), (142, 'Brenda Williams', 57, 'Kenya', 'DevOps Engineer', 'Monitoring, CI/CD, Containerization, Cloud Computing', 'PHD', 14, 'Spanish Intermediate'), (143, 'Douglas Webster', 48, 'Saint Helena', 'Singer', None, 'PHD', 3, 'French Fluent'), (144, 'Terry Elliott', 48, 'Turks and Caicos Islands', 'Teacher', None, 'Commercial License', 1, 'Spanish Fluent'), (145, 'Melissa Ortega', 30, 'Central African Republic', 'Test Engineer', None, 'M Tech', 11, None), (146, 'Karen Rodriguez PhD', 48, 'Sierra Leone', 'Nurse', 'Surgery, Wound Care, Pediatrics', 'Diploma', 7, None), (147, 'Troy Ryan', 26, 'Congo', 'Test Engineer', None, 'Diploma', 14, 'Chinese Fluent'), (148, 'Jeremy Stein', 32, 'Zimbabwe', 'AI Engineer', None, 'BSc Nursing', 15, 'English Basic'), (149, 'Daniel Miller', 23, 'Monaco', 'Cricketer', None, None, 14, 'Chinese Fluent'), (150, 'Cheryl Reed', 30, 'Colombia', 'Cricketer', None, 'Diploma', 8, 'English Fluent'), (151, 'Eric Holland', 35, 'Oman', 'Network Administrator', 'Network Design, Network Security', 'Diploma', 2, 'Chinese Intermediate'), (152, 'Austin Velez', 46, 'Isle of Man', 'Network Administrator', 'Network Design', 'Commercial License', 18, 'German B1'), (153, 'Kelly Lopez', 56, 'Bouvet Island (Bouvetoya)', 'Movie Director', None, 'M Tech', 20, 'French Intermediate'), (154, 'Michael Christian', 23, 'Gibraltar', 'Data Scientist', 'Data Visualization, Machine Learning, Data Analysis, Statistics', 'Associate Degree', 8, None), (155, 'Tiffany Johnson', 58, 'Saudi Arabia', 'Data Scientist', 'Data Analysis', 'Certificate Program', 19, 'Spanish Basic'), (156, 'Stephen Church', 45, 'Saint Lucia', 'Data Scientist', 'Data Visualization, Data Analysis, Machine Learning, Statistics', 'M Tech', 20, 'Spanish Fluent'), (157, 'Lauren Larsen DDS', 26, 'Romania', 'Engineer', 'Civil Engineering', 'Commercial License', 16, 'German C1'), (158, 'Stacy Nicholson', 55, 'Tokelau', 'Dancer', None, 'Certificate Program', 15, 'German B2'), (159, 'Jessica Bean', 60, 'Gambia', 'Database Administrator', 'Backup and Recovery', 'PHD', 19, 'Chinese Fluent'), (160, 'Sheri Anderson', 30, 'Eritrea', 'Painter', None, 'PHD', 8, 'German A1'), (161, 'Sarah Dixon', 45, 'Namibia', 'Pilot', None, None, 14, 'French Fluent'), (162, 'Michele Davis', 28, 'Central African Republic', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 9, 'Chinese Intermediate'), (163, 'Glenn Mejia', 52, 'Antarctica (the territory South of 60 deg S)', 'Office Driver', None, 'BSc Nursing', 3, 'English Intermediate'), (164, 'Susan Riggs', 28, 'Cameroon', 'Teacher', None, None, 18, 'French Fluent'), (165, 'Lori Franklin PhD', 25, 'Lebanon', 'AI Engineer', 'Natural Language Processing', None, 11, 'Chinese Basic'), (166, 'Michelle Saunders', 53, 'Niue', 'Movie Producer', None, 'High School', 2, 'French Intermediate'), (167, 'Brian Wise', 47, 'Palestinian Territory', 'UX/UI Designer', 'Usability Testing, User Research, Prototyping', 'Diploma', 10, 'German C2'), (168, 'Ryan Miller', 22, 'Bahamas', 'Athelete', None, 'Associate Degree', 2, None), (169, 'Christopher Lopez', 28, 'Montenegro', 'DevOps Engineer', None, 'M Tech', 3, None), (170, 'Benjamin Scott', 38, 'Suriname', 'Database Administrator', None, 'PHD', 15, 'French Basic'), (171, 'Mark Carson', 53, 'Nicaragua', 'Cricketer', None, \"Master's Degree\", 14, 'German B2'), (172, 'Helen Simmons', 38, 'Isle of Man', 'Cricketer', None, \"Master's Degree\", 16, 'Spanish Basic'), (173, 'Krista Moore', 36, 'Cambodia', 'Test Engineer', None, 'Certificate Program', 13, 'German C1'), (174, 'Jonathan Hernandez', 37, 'South Georgia and the South Sandwich Islands', 'Teacher', None, 'PHD', 8, 'Chinese Intermediate'), (175, 'Lisa Griffin', 30, 'Bouvet Island (Bouvetoya)', 'Data Scientist', 'Data Analysis, Statistics', None, 1, None), (176, 'Beth Espinoza', 52, 'Tajikistan', 'Nurse', 'Emergency', None, 19, 'Spanish Fluent'), (177, 'Terry Aguirre', 56, 'Cyprus', 'Cricketer', None, 'Commercial License', 15, 'English Basic'), (178, 'Mr. Richard Smith', 34, 'Palau', 'Teacher', None, \"Master's Degree\", 2, 'Chinese Basic'), (179, 'Phillip Ruiz', 25, 'Sweden', 'Test Engineer', None, 'Certificate Program', 15, None), (180, 'Maria Brooks', 53, 'Albania', 'Painter', None, None, 15, 'Chinese Intermediate'), (181, 'Mark Hall', 49, 'Holy See (Vatican City State)', 'Cybersecurity Specialist', None, 'High School', 16, 'Chinese Intermediate'), (182, 'Karen Welch', 41, 'United Arab Emirates', 'Singer', None, 'Certificate Program', 20, 'French Fluent'), (183, 'Alyssa Howard', 36, 'Heard Island and McDonald Islands', 'Software Developer', 'Agile Development, Python, Java, JavaScript', \"Master's Degree\", 11, 'Chinese Intermediate'), (184, 'Mr. Jason Smith', 47, 'Estonia', 'Engineer', 'Civil Engineering', 'M Tech', 14, None), (185, 'Robert Baker', 30, 'Taiwan', 'Movie Producer', None, 'B Tech', 19, 'English Basic'), (186, 'Robert Scott', 49, 'Spain', 'Painter', None, 'Commercial License', 6, 'English Basic'), (187, 'Destiny Graves', 39, 'Hong Kong', 'Engineer', 'Chemical Engineering, Civil Engineering, Electrical Engineering, Mechanical Engineering', \"Bachelor's Degree\", 5, 'French Basic'), (188, 'James Johns', 52, 'Spain', 'Singer', None, 'PHD', 11, 'Chinese Basic'), (189, 'David Boyd', 26, 'Ecuador', 'Social Media Influencer', None, \"Bachelor's Degree\", 14, 'German A1'), (190, 'Angela Woods', 30, 'Greenland', 'Engineer', 'Electrical Engineering', 'Diploma', 11, 'German A2'), (191, 'James Novak', 35, 'Kenya', 'Painter', None, 'B Tech', 4, 'German B1'), (192, 'Phillip Hogan', 27, \"Cote d'Ivoire\", 'Dancer', None, 'M Tech', 11, None), (193, 'Alexandra Black', 31, 'Chad', 'Cricketer', None, 'BSc Nursing', 19, 'Spanish Basic'), (194, 'Megan Newman', 54, 'Cayman Islands', 'Dancer', None, None, 11, None), (195, 'Andre Griffin', 28, 'Western Sahara', 'Nurse', 'ICU, Surgery, Wound Care, Emergency, Long-Term Care, Pediatrics', 'Associate Degree', 20, 'Chinese Fluent'), (196, 'Jean Drake', 59, 'Switzerland', 'School Bus Driver', 'Passenger Endorsement', 'BSc Nursing', 18, 'Spanish Basic'), (197, 'Regina Price', 39, 'Liechtenstein', 'Pilot', None, None, 10, 'English Basic'), (198, 'Nancy Cortez', 45, 'Western Sahara', 'Gym Trainer', None, None, 16, None), (199, 'Natalie Howard', 35, 'Aruba', 'Software Developer', 'Python, C++', 'Certificate Program', 6, 'German A1'), (200, 'Shawn Richardson', 40, 'Togo', 'Teacher', None, \"Master's Degree\", 11, 'Spanish Intermediate'), (201, 'Dr. Emily Scott DDS', 37, 'Kiribati', 'Licensed Practical Nurse', None, \"Master's Degree\", 7, 'English Basic'), (202, 'Michelle Lopez', 33, 'Trinidad and Tobago', 'Network Administrator', None, 'B Tech', 13, 'Chinese Fluent'), (203, 'Caitlin Buchanan', 25, 'Cook Islands', 'Movie Producer', None, None, 2, 'German A1'), (204, 'Tara Smith', 35, 'Congo', 'Dancer', None, \"Bachelor's Degree\", 8, 'Spanish Fluent'), (205, 'Heather Patel', 26, 'Niue', 'DevOps Engineer', 'Containerization, Monitoring, CI/CD, Cloud Computing', 'M Tech', 11, 'Spanish Intermediate'), (206, 'Kevin Mckinney', 47, 'Timor-Leste', 'Nurse', None, 'M Tech', 8, 'English Basic'), (207, 'Kim Martin', 22, 'Saint Vincent and the Grenadines', 'Dancer', None, 'B Tech', 18, 'French Intermediate'), (208, 'Hunter Morgan', 54, 'Congo', 'Test Engineer', None, 'B Tech', 16, 'English Basic'), (209, 'Jonathan Washington', 45, 'Equatorial Guinea', 'UX/UI Designer', None, 'PHD', 15, 'English Basic'), (210, 'Nathaniel Sanchez', 26, 'Burkina Faso', 'Database Administrator', 'Backup and Recovery, Performance Tuning, Database Design', 'Commercial License', 12, 'Spanish Intermediate'), (211, 'Theresa Murillo', 35, 'Romania', 'Truck Driver', None, 'Certificate Program', 17, 'Chinese Intermediate'), (212, 'Carrie Jackson', 59, 'Tanzania', 'Nurse', 'Pediatrics, Long-Term Care, Surgery, ICU', \"Bachelor's Degree\", 15, 'English Fluent'), (213, 'Melissa Perez', 55, 'Cyprus', 'Social Media Influencer', None, \"Bachelor's Degree\", 7, 'Chinese Fluent'), (214, 'Michael Moreno', 60, 'Ethiopia', 'School Bus Driver', 'Passenger Endorsement', 'High School', 20, 'Spanish Basic'), (215, 'Kerry Jones', 45, 'Fiji', 'Dancer', None, 'M Tech', 12, 'Spanish Intermediate'), (216, 'Beth Austin', 28, \"Lao People's Democratic Republic\", 'Dancer', None, \"Master's Degree\", 2, 'German B2'), (217, 'Jon Ewing', 56, 'Nauru', 'Painter', None, \"Bachelor's Degree\", 7, 'Spanish Fluent'), (218, 'Jason Kane', 39, 'Djibouti', 'Test Engineer', None, None, 16, 'French Basic'), (219, 'Scott Patel', 54, 'Bolivia', 'Office Driver', None, 'M Tech', 20, 'English Intermediate'), (220, 'Mrs. Lisa Warren', 36, 'Solomon Islands', 'Test Engineer', None, 'Commercial License', 15, 'German B1'), (221, 'Bill Bowers', 36, 'Tunisia', 'Dancer', None, None, 15, 'French Basic'), (222, 'Maria Ortiz', 55, 'Turkey', 'Gym Trainer', None, 'Commercial License', 20, 'Chinese Intermediate'), (223, 'Robert Warner', 24, 'Wallis and Futuna', 'Movie Director', None, 'Diploma', 3, None), (224, 'David Nelson', 39, 'Angola', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'Diploma', 13, None), (225, 'Donna Meyers', 40, 'Monaco', 'Athelete', None, None, 1, 'Spanish Intermediate'), (226, 'Oscar Velez', 32, 'Norfolk Island', 'Cricketer', None, None, 11, 'Chinese Fluent'), (227, 'John Long', 54, 'Estonia', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Certificate Program', 12, 'Spanish Intermediate'), (228, 'Susan Sherman', 43, 'Saint Kitts and Nevis', 'Dancer', None, 'High School', 4, None), (229, 'Kristin Levine', 46, 'United States of America', 'Social Media Influencer', None, \"Master's Degree\", 11, 'German A1'), (230, 'Philip Swanson', 41, 'Sierra Leone', 'Data Scientist', 'Data Visualization, Statistics, Data Analysis', 'Commercial License', 9, 'German B2'), (231, 'Theodore Carter', 57, 'Bosnia and Herzegovina', 'Engineer', None, 'High School', 12, 'German A2'), (232, 'Nathan Crawford MD', 50, 'Turkmenistan', 'Athelete', None, \"Master's Degree\", 9, 'Spanish Fluent'), (233, 'Jimmy Neal', 44, 'Turks and Caicos Islands', 'School Bus Driver', None, 'PHD', 5, 'French Intermediate'), (234, 'Jessica Tapia', 26, 'Namibia', 'Licensed Practical Nurse', None, 'B Tech', 7, None), (235, 'Ricky Noble', 35, 'Norway', 'Professor', None, 'High School', 9, 'English Basic'), (236, 'Michael Hudson', 59, 'Poland', 'Engineer', None, 'Certificate Program', 5, 'Spanish Fluent'), (237, 'Yolanda Doyle', 37, 'Tokelau', 'DevOps Engineer', 'Monitoring', 'Certificate Program', 15, 'English Intermediate'), (238, 'Amy Scott', 54, 'Paraguay', 'Engineer', None, 'M Tech', 1, 'German C1'), (239, 'Frances Dean', 29, 'Cocos (Keeling) Islands', 'Software Developer', 'Agile Development, Java, Python', \"Bachelor's Degree\", 16, 'German B2'), (240, 'Robert Griffith', 49, 'El Salvador', 'UX/UI Designer', 'Wireframing, User Research', 'PHD', 16, 'English Basic'), (241, 'John Lawson', 23, 'Grenada', 'Dancer', None, None, 12, 'Chinese Basic'), (242, 'Mrs. Stefanie Finley', 42, 'Angola', 'DevOps Engineer', None, 'High School', 5, 'Chinese Fluent'), (243, 'Jenna Mcknight', 33, 'Samoa', 'UX/UI Designer', 'User Research, Prototyping', None, 8, 'Chinese Fluent'), (244, 'Tracy Green', 39, 'Burkina Faso', 'School Bus Driver', None, 'Certificate Program', 4, 'Spanish Basic'), (245, 'Nicholas Dunn', 57, 'Oman', 'Cybersecurity Specialist', 'Threat Analysis', 'BSc Nursing', 13, 'English Fluent'), (246, 'Abigail Taylor', 30, 'Haiti', 'Teacher', None, 'Diploma', 14, 'German A2'), (247, 'Kelly Hall', 50, 'Costa Rica', 'Painter', None, 'B Tech', 11, 'Chinese Intermediate'), (248, 'Ebony Lopez', 42, 'Italy', 'Cricketer', None, None, 15, 'Chinese Fluent'), (249, 'Katie Beck', 43, 'Honduras', 'AI Engineer', None, \"Bachelor's Degree\", 9, None), (250, 'Marie Peters', 34, 'Suriname', 'Test Engineer', None, 'PHD', 19, 'German B1'), (251, 'Toni Short', 39, 'Comoros', 'Engineer', 'Civil Engineering', 'B Tech', 13, None), (252, 'Richard Rose', 52, 'Grenada', 'Truck Driver', 'Hazmat, Tanker, Defensive Driving, OTR', 'Diploma', 8, 'Chinese Basic'), (253, 'Christopher Bentley', 42, 'Timor-Leste', 'Painter', None, 'High School', 12, 'Spanish Fluent'), (254, 'William Neal', 38, 'Denmark', 'Data Scientist', 'Statistics, Data Visualization', 'Commercial License', 11, None), (255, 'Preston Savage', 54, 'Morocco', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', 'Certificate Program', 18, None), (256, 'Philip Ryan', 54, 'Botswana', 'Truck Driver', 'OTR, Defensive Driving, Hazmat, Tanker', 'Certificate Program', 20, 'English Basic'), (257, 'Jennifer Moore', 37, 'Israel', 'Data Scientist', None, 'Certificate Program', 2, None), (258, 'Kelly Gonzalez', 51, 'Guadeloupe', 'Database Administrator', None, 'High School', 11, 'Chinese Fluent'), (259, 'Michael Young', 52, 'Mali', 'Database Administrator', 'Database Design', 'PHD', 20, 'Spanish Basic'), (260, 'Lynn Welch', 41, 'Zambia', 'Network Administrator', 'Network Design', None, 9, 'English Fluent'), (261, 'Dana Smith', 59, 'Norway', 'Dancer', None, 'Diploma', 11, 'Spanish Intermediate'), (262, 'Andrea Sanchez', 32, 'Belize', 'School Bus Driver', 'Defensive Driving, Passenger Endorsement', \"Bachelor's Degree\", 7, 'Chinese Basic'), (263, 'Maurice Murray', 29, 'Ireland', 'Engineer', None, 'PHD', 7, 'German C2'), (264, 'Bill Collier', 29, 'Western Sahara', 'Athelete', None, \"Master's Degree\", 4, 'English Fluent'), (265, 'Aaron Hernandez', 28, 'Saudi Arabia', 'Cybersecurity Specialist', 'Threat Analysis', None, 11, None), (266, 'Suzanne Cooper', 46, 'Latvia', 'Cybersecurity Specialist', 'Network Security, Threat Analysis, Compliance', 'Associate Degree', 3, 'English Intermediate'), (267, 'Jeremy Taylor', 56, 'Equatorial Guinea', 'Office Driver', None, \"Bachelor's Degree\", 19, 'French Intermediate'), (268, 'Christopher Coleman', 34, 'Saint Pierre and Miquelon', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis', 'Commercial License', 2, 'German A1'), (269, 'Sara Garcia', 32, 'Somalia', 'Product Manager', None, 'BSc Nursing', 2, None), (270, 'Angel Lee', 56, 'Hungary', 'Truck Driver', None, 'Certificate Program', 16, None), (271, 'Jamie Sparks', 41, 'Philippines', 'Gym Trainer', None, 'Diploma', 10, 'German C2'), (272, 'Brian Mcgrath', 51, 'Bulgaria', 'Office Driver', None, 'Certificate Program', 12, None), (273, 'Christine Coleman', 28, 'Algeria', 'Nurse', 'Long-Term Care, Pediatrics, ICU', 'Associate Degree', 6, 'English Basic'), (274, 'Bryan Greene', 26, 'Norfolk Island', 'AI Engineer', 'Computer Vision, Deep Learning, Robotics', None, 14, None), (275, 'Mr. Cody Harper', 26, 'Suriname', 'Truck Driver', 'Defensive Driving, Tanker, Hazmat, OTR', 'Associate Degree', 17, None), (276, 'Margaret Brown', 53, 'Switzerland', 'Cybersecurity Specialist', 'Threat Analysis, Incident Response, Compliance, Network Security', None, 20, 'German B2'), (277, 'Jack Scott', 47, 'Netherlands', 'DevOps Engineer', 'CI/CD, Containerization', \"Master's Degree\", 3, 'German C1'), (278, 'Becky Stanley', 50, 'Tajikistan', 'Cybersecurity Specialist', 'Compliance, Network Security', None, 10, 'Chinese Fluent'), (279, 'Sean Durham', 29, 'Seychelles', 'Software Developer', None, None, 20, None), (280, 'Teresa Thomas', 60, 'China', 'Cricketer', None, 'Associate Degree', 12, None), (281, 'Chelsea Mccormick', 49, 'Costa Rica', 'Test Engineer', None, None, 12, None), (282, 'Jonathan Brooks', 44, 'Barbados', 'Painter', None, 'BSc Nursing', 10, 'English Intermediate'), (283, 'Michelle Coleman', 55, 'Costa Rica', 'Movie Producer', None, 'Certificate Program', 1, 'Spanish Basic'), (284, 'Gabriel Cox', 46, 'Aruba', 'Teacher', None, \"Master's Degree\", 18, 'English Fluent'), (285, 'Nicholas Tucker', 60, 'Brunei Darussalam', 'Movie Producer', None, 'High School', 2, 'Spanish Intermediate'), (286, 'Kendra Blevins', 32, 'Isle of Man', 'Software Developer', 'Python, Java, JavaScript, Agile Development, C++', 'B Tech', 20, 'Chinese Fluent'), (287, 'Courtney Schaefer', 44, 'Montserrat', 'DevOps Engineer', 'Containerization, CI/CD', 'BSc Nursing', 7, None), (288, 'Sarah Cain', 26, 'Sri Lanka', 'Cybersecurity Specialist', 'Incident Response, Threat Analysis, Network Security', 'B Tech', 15, 'German C2'), (289, 'James Ramos', 56, 'Guam', 'Social Media Influencer', None, 'Certificate Program', 15, 'Chinese Fluent'), (290, 'Ryan Frank', 53, 'Togo', 'Dancer', None, 'High School', 17, 'Chinese Fluent'), (291, 'Joel Farmer', 22, 'Sierra Leone', 'Gym Trainer', None, 'Certificate Program', 8, None), (292, 'John Medina', 46, 'Swaziland', 'Social Media Influencer', None, \"Bachelor's Degree\", 16, 'French Intermediate'), (293, 'Cheryl Allen', 29, 'Maldives', 'UX/UI Designer', 'User Research, Usability Testing, Prototyping', \"Bachelor's Degree\", 13, 'English Basic'), (294, 'Collin Evans', 41, 'Sri Lanka', 'Office Driver', None, \"Master's Degree\", 6, 'Spanish Fluent'), (295, 'Paul Soto', 60, 'Niue', 'Gym Trainer', None, 'PHD', 8, 'French Intermediate'), (296, 'Daniel Hall', 38, 'Antigua and Barbuda', 'Cricketer', None, 'M Tech', 18, 'French Basic'), (297, 'Kyle Clayton', 60, 'Croatia', 'Painter', None, \"Bachelor's Degree\", 5, 'English Basic'), (298, 'Elizabeth Ward', 43, 'Pakistan', 'Professor', None, None, 12, None), (299, 'Michael Perkins', 23, 'Kuwait', 'Professor', None, None, 18, 'Chinese Fluent'), (300, 'Katelyn Carroll', 45, 'Sierra Leone', 'Movie Producer', None, 'Certificate Program', 3, 'French Basic'), (301, 'William Banks', 46, 'Tunisia', 'Cybersecurity Specialist', 'Network Security, Compliance, Incident Response, Threat Analysis', 'Associate Degree', 14, 'English Fluent'), (302, 'Jose Powers', 25, 'Congo', 'Painter', None, 'Diploma', 20, 'French Fluent'), (303, 'Grace Hendricks', 58, 'Barbados', 'Data Scientist', 'Data Analysis, Data Visualization, Statistics, Machine Learning', 'B Tech', 9, None), (304, 'Sarah Blake', 27, 'Cayman Islands', 'Office Driver', None, 'High School', 1, 'English Intermediate'), (305, 'Richard Tran', 22, 'Christmas Island', 'Movie Director', None, 'Associate Degree', 7, 'Spanish Intermediate'), (306, 'Edwin Carlson', 22, 'Greece', 'AI Engineer', None, 'PHD', 5, 'English Basic'), (307, 'Nina Singh', 37, 'Cook Islands', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'High School', 19, 'Spanish Fluent'), (308, 'Kimberly Robinson', 39, 'Holy See (Vatican City State)', 'Test Engineer', None, 'Associate Degree', 9, 'Spanish Fluent'), (309, 'Michael Schwartz', 39, 'Sri Lanka', 'Database Administrator', None, 'M Tech', 17, 'English Intermediate'), (310, 'Craig Johnson', 22, 'Russian Federation', 'Test Engineer', None, 'Certificate Program', 7, None), (311, 'Kevin Bailey', 56, 'Benin', 'Professor', None, 'Associate Degree', 3, 'German B1'), (312, 'Leonard Hill', 34, 'Central African Republic', 'Cybersecurity Specialist', None, 'M Tech', 4, None), (313, 'Daryl Watson', 29, 'Slovakia (Slovak Republic)', 'UX/UI Designer', 'User Research, Wireframing, Usability Testing', None, 18, None), (314, 'Troy Baker', 26, 'Puerto Rico', 'Dancer', None, 'M Tech', 2, 'French Intermediate'), (315, 'Matthew Martin', 43, 'Taiwan', 'Test Engineer', None, 'B Tech', 7, 'Spanish Fluent'), (316, 'Katrina Evans', 22, 'Comoros', 'Athelete', None, 'Certificate Program', 7, 'Chinese Basic'), (317, 'Shannon Chang', 28, 'Saint Lucia', 'Cricketer', None, 'Associate Degree', 17, 'English Intermediate'), (318, 'Jesus Stewart', 29, 'Vanuatu', 'UX/UI Designer', None, \"Bachelor's Degree\", 19, 'German A2'), (319, 'Andrew Powell', 44, 'Australia', 'Singer', None, 'B Tech', 12, None), (320, 'Heather Graham', 36, 'Dominica', 'Database Administrator', None, 'B Tech', 12, 'German B2'), (321, 'Laura Adams', 52, 'China', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', None, 9, 'Chinese Basic'), (322, 'Melissa Waller', 27, 'Falkland Islands (Malvinas)', 'DevOps Engineer', None, 'High School', 3, None), (323, 'Edward Mccoy', 27, 'Cameroon', 'Truck Driver', 'Tanker, Hazmat, OTR', None, 9, 'Spanish Basic'), (324, 'Amy Myers', 38, 'Nigeria', 'School Bus Driver', None, 'BSc Nursing', 6, 'Chinese Basic'), (325, 'Kelly Odonnell', 30, 'Antigua and Barbuda', 'Engineer', 'Mechanical Engineering, Electrical Engineering, Chemical Engineering', 'B Tech', 15, None), (326, 'Robert Robertson', 34, 'French Southern Territories', 'Engineer', 'Mechanical Engineering, Civil Engineering, Electrical Engineering', 'M Tech', 2, None), (327, 'Phillip Ewing', 30, 'Spain', 'Data Scientist', 'Statistics, Data Visualization, Machine Learning', None, 9, 'Spanish Intermediate'), (328, 'Shelby Butler', 22, 'Indonesia', 'Test Engineer', None, None, 12, None), (329, 'Carla Haney', 23, 'Lesotho', 'Database Administrator', 'Database Design, Backup and Recovery', \"Bachelor's Degree\", 5, 'Spanish Intermediate'), (330, 'Tina Martin', 35, 'Germany', 'Truck Driver', 'Defensive Driving, Hazmat, Tanker', 'Certificate Program', 17, 'English Intermediate'), (331, 'Margaret Alexander', 60, 'Andorra', 'Cybersecurity Specialist', 'Compliance, Threat Analysis', 'Associate Degree', 14, 'German A1'), (332, 'Julie Lindsey', 30, 'Montenegro', 'DevOps Engineer', 'Monitoring, CI/CD', 'Commercial License', 8, 'German A1'), (333, 'Robert White', 58, 'Montenegro', 'Database Administrator', 'Backup and Recovery, Database Design', 'PHD', 11, 'French Basic'), (334, 'Ryan Cameron', 39, 'Brunei Darussalam', 'Pilot', None, 'High School', 10, 'Spanish Basic'), (335, 'Margaret Miller', 39, 'Burkina Faso', 'Painter', None, 'M Tech', 11, 'Chinese Basic'), (336, 'Benjamin Howard', 51, 'Montenegro', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', None, 1, 'Chinese Basic'), (337, 'Michelle Ferrell', 24, 'Tajikistan', 'Office Driver', None, 'Commercial License', 20, 'Spanish Basic'), (338, 'Amber Watson', 31, 'French Southern Territories', 'Licensed Practical Nurse', 'Wound Care, Long-Term Care', 'M Tech', 20, 'German B2'), (339, 'Anne Simpson', 33, 'Tuvalu', 'Pilot', None, 'Associate Degree', 9, 'German C1'), (340, 'Melissa Rubio', 39, 'Russian Federation', 'Office Driver', None, None, 7, 'Chinese Basic'), (341, 'Robin Villarreal', 26, 'American Samoa', 'DevOps Engineer', 'Cloud Computing', 'Diploma', 20, 'Chinese Fluent'), (342, 'Charles Duran', 51, 'Macao', 'Network Administrator', 'Network Design, Network Security', 'High School', 1, 'Chinese Fluent'), (343, 'Heather Booth', 51, 'Denmark', 'Pilot', None, \"Bachelor's Degree\", 13, 'German B1'), (344, 'Lori Morrison', 23, 'Ireland', 'Engineer', None, None, 17, None), (345, 'Taylor Figueroa', 30, 'Chile', 'DevOps Engineer', None, None, 12, 'Spanish Intermediate'), (346, 'Christopher Baker', 40, 'Palau', 'Software Developer', None, \"Bachelor's Degree\", 12, 'English Fluent'), (347, 'Billy Mccormick', 57, \"Lao People's Democratic Republic\", 'UX/UI Designer', 'User Research, Wireframing', None, 17, 'French Basic'), (348, 'Michele Orozco', 31, 'Palau', 'Gym Trainer', None, 'BSc Nursing', 8, 'Spanish Intermediate'), (349, 'Krystal Meyer', 35, 'Kiribati', 'Cybersecurity Specialist', None, \"Master's Degree\", 16, None), (350, 'Vincent Lee', 32, 'Ethiopia', 'Office Driver', None, \"Bachelor's Degree\", 4, 'Spanish Intermediate'), (351, 'Tara Jones', 33, 'Saint Martin', 'Office Driver', None, None, 6, None), (352, 'Elizabeth Gonzales MD', 29, 'Namibia', 'Database Administrator', 'Database Design, Performance Tuning, Backup and Recovery', 'High School', 16, 'English Fluent'), (353, 'Katelyn Vega', 38, 'Brazil', 'Software Developer', 'Python, C++, Java, Agile Development, JavaScript', 'High School', 11, 'German C1'), (354, 'Sherri York', 23, 'Western Sahara', 'Test Engineer', None, \"Master's Degree\", 6, 'French Fluent'), (355, 'Jeff Bradley', 58, 'Honduras', 'Software Developer', None, 'M Tech', 14, 'Chinese Basic'), (356, 'Joshua Brown', 34, 'Turks and Caicos Islands', 'Pilot', None, 'Certificate Program', 16, 'French Intermediate'), (357, 'Kenneth Macias', 40, 'Mayotte', 'Licensed Practical Nurse', 'Long-Term Care, Wound Care', 'BSc Nursing', 20, 'English Basic'), (358, 'Cassandra Williams MD', 35, 'Belarus', 'Cybersecurity Specialist', None, \"Bachelor's Degree\", 12, 'German C1'), (359, 'Ashley Erickson', 39, 'Nigeria', 'Dancer', None, None, 9, 'Chinese Basic'), (360, 'Lee Molina', 58, 'Russian Federation', 'Office Driver', None, 'Diploma', 12, 'English Intermediate'), (361, 'Gary Vasquez', 42, 'Togo', 'Engineer', None, 'M Tech', 7, 'Spanish Fluent'), (362, 'Victoria Goodman', 42, 'Mayotte', 'Data Scientist', 'Statistics, Data Visualization', 'BSc Nursing', 6, None), (363, 'Karen Roberts', 26, 'Bangladesh', 'Athelete', None, 'PHD', 11, 'Spanish Intermediate'), (364, 'James Stevens', 31, 'Slovakia (Slovak Republic)', 'Pilot', None, None, 12, 'French Fluent'), (365, 'Timothy Fuentes', 54, 'Nigeria', 'Test Engineer', None, 'High School', 15, 'Chinese Fluent'), (366, 'Christopher Thompson', 60, 'Ukraine', 'Engineer', None, 'Certificate Program', 8, 'Chinese Fluent'), (367, 'Daniel Holloway', 28, 'Seychelles', 'Truck Driver', 'Tanker, Hazmat, OTR, Defensive Driving', 'M Tech', 13, 'Spanish Basic'), (368, 'Adam Hill', 52, 'Guernsey', 'Product Manager', None, 'B Tech', 6, None), (369, 'William Lewis', 52, 'Kenya', 'Software Developer', None, 'PHD', 20, None), (370, 'Jonathan Flowers', 50, 'Antarctica (the territory South of 60 deg S)', 'Cricketer', None, None, 1, 'English Fluent'), (371, 'Terry Lee', 44, 'New Zealand', 'Test Engineer', None, 'B Tech', 8, 'Spanish Basic'), (372, 'Miranda Chaney', 48, 'Slovenia', 'Pilot', None, 'M Tech', 18, 'Spanish Intermediate'), (373, 'Mark Ford', 41, 'Cayman Islands', 'Athelete', None, 'High School', 13, 'German A2'), (374, 'Gregory Brown', 27, 'Liberia', 'AI Engineer', None, 'PHD', 10, 'Chinese Basic'), (375, 'Erica Cohen', 58, 'Kiribati', 'Network Administrator', None, 'BSc Nursing', 18, 'Chinese Intermediate'), (376, 'Mrs. Danielle Williams', 47, 'Azerbaijan', 'Network Administrator', None, 'High School', 19, 'German C2'), (377, 'Dawn Hurley', 53, 'South Africa', 'DevOps Engineer', None, 'Commercial License', 2, 'English Intermediate'), (378, 'Mrs. Michelle Thompson', 36, 'United States Minor Outlying Islands', 'Licensed Practical Nurse', None, \"Bachelor's Degree\", 5, None), (379, 'Leslie Fields', 53, 'Ukraine', 'Product Manager', 'Market Research, Project Management, Product Development', 'PHD', 8, None), (380, 'Sandra Webb', 50, 'Croatia', 'Database Administrator', 'Database Design, Performance Tuning', 'Diploma', 13, None), (381, 'Angela Burns', 54, 'Botswana', 'Athelete', None, 'B Tech', 1, 'German C2'), (382, 'Christian Evans', 29, 'Ecuador', 'Athelete', None, 'M Tech', 8, 'German B2'), (383, 'April Rasmussen', 45, 'Zambia', 'Dancer', None, 'High School', 12, 'English Intermediate'), (384, 'Douglas Blackburn', 44, 'Niue', 'Database Administrator', None, None, 1, 'Chinese Fluent'), (385, 'Richard Hernandez', 33, 'Isle of Man', 'Singer', None, 'M Tech', 20, 'French Fluent'), (386, 'Amy Miller', 40, 'Oman', 'Licensed Practical Nurse', None, 'PHD', 20, None), (387, 'Charles Garcia', 49, 'Syrian Arab Republic', 'Office Driver', None, None, 16, None), (388, 'Tara Marks', 56, 'Antigua and Barbuda', 'Movie Director', None, \"Bachelor's Degree\", 2, 'French Basic'), (389, 'John Calhoun', 35, 'Cocos (Keeling) Islands', 'Data Scientist', 'Data Visualization, Machine Learning, Statistics', None, 7, 'German C1'), (390, 'Kevin Brown', 36, 'Eritrea', 'Dancer', None, \"Bachelor's Degree\", 8, 'English Basic'), (391, 'Joshua Vang', 53, 'Georgia', 'Painter', None, 'Certificate Program', 8, None), (392, 'Brooke Nixon', 24, 'Canada', 'Cricketer', None, 'PHD', 20, 'Spanish Intermediate'), (393, 'Sarah Evans', 33, 'Australia', 'Database Administrator', 'Backup and Recovery, Database Design, Performance Tuning', 'PHD', 12, None), (394, 'Jacqueline Howard', 59, 'Iraq', 'Social Media Influencer', None, 'B Tech', 13, 'French Basic'), (395, 'James James', 28, 'Mayotte', 'School Bus Driver', 'Passenger Endorsement, Defensive Driving', 'Diploma', 17, 'English Basic'), (396, 'Kristin Park', 49, 'Oman', 'Dancer', None, 'High School', 15, 'English Intermediate'), (397, 'Lisa Pacheco', 51, 'Luxembourg', 'Athelete', None, 'Certificate Program', 9, 'French Intermediate'), (398, 'Linda Higgins', 24, 'Niue', 'AI Engineer', 'Computer Vision, Robotics, Natural Language Processing, Deep Learning', 'Certificate Program', 16, 'German B1'), (399, 'Corey Yang', 44, 'Netherlands Antilles', 'Nurse', 'Pediatrics, Surgery', 'Associate Degree', 6, 'Chinese Intermediate'), (400, 'Carolyn Black', 53, 'Myanmar', 'Nurse', None, 'High School', 20, None), (401, 'Dana Luna', 54, 'Taiwan', 'Engineer', 'Chemical Engineering, Civil Engineering', 'Commercial License', 19, None), (402, 'Jacob Mendez', 25, 'Bosnia and Herzegovina', 'Test Engineer', None, None, 17, 'Spanish Basic'), (403, 'James Coleman', 58, 'Sao Tome and Principe', 'Social Media Influencer', None, 'M Tech', 9, 'German B1'), (404, 'Gary Garcia', 40, 'Niue', 'School Bus Driver', 'Defensive Driving', 'Certificate Program', 12, 'Chinese Intermediate'), (405, 'Danny Arnold', 25, 'British Virgin Islands', 'Network Administrator', 'Troubleshooting, Network Security, Network Design', 'M Tech', 19, 'French Fluent'), (406, 'Michael Young', 58, 'Slovakia (Slovak Republic)', 'Network Administrator', 'Network Security, Network Design', 'Diploma', 13, 'French Basic'), (407, 'Amanda Thomas', 46, 'North Macedonia', 'Test Engineer', None, 'Associate Degree', 11, 'Spanish Intermediate'), (408, 'Rodney Jones', 39, 'Bermuda', 'Athelete', None, 'PHD', 4, None), (409, 'Jose Jones', 47, 'Gabon', 'Social Media Influencer', None, 'B Tech', 6, 'German A2'), (410, 'Angela Herrera', 37, 'Svalbard & Jan Mayen Islands', 'Painter', None, 'M Tech', 1, 'English Fluent'), (411, 'Angela Sanchez', 49, 'Niue', 'Database Administrator', 'Performance Tuning, Database Design, Backup and Recovery', 'Associate Degree', 7, 'German C1'), (412, 'Terri Smith', 48, 'Ghana', 'Movie Director', None, 'Commercial License', 20, 'Spanish Intermediate'), (413, 'Jessica Bell', 40, 'Luxembourg', 'Product Manager', 'Project Management, Product Development, Market Research', 'M Tech', 18, 'Spanish Basic'), (414, 'Thomas Mcdonald', 51, 'Congo', 'Athelete', None, 'Commercial License', 16, 'German B2'), (415, 'Brent Durham', 54, 'Burundi', 'AI Engineer', 'Natural Language Processing, Computer Vision', 'B Tech', 11, 'French Intermediate'), (416, 'Joanna Greene', 26, 'Afghanistan', 'Test Engineer', None, 'Certificate Program', 8, None), (417, 'Keith Bauer', 51, 'French Guiana', 'Engineer', None, 'M Tech', 5, 'Chinese Intermediate'), (418, 'Jessica Crosby', 39, 'Bahamas', 'Cybersecurity Specialist', None, 'PHD', 10, 'Chinese Basic'), (419, 'Stacey Ramirez', 37, 'Liechtenstein', 'Product Manager', 'Product Development, Market Research', 'High School', 18, 'Spanish Fluent'), (420, 'Jennifer Perez', 60, 'Sri Lanka', 'Cybersecurity Specialist', None, 'High School', 6, None), (421, 'Bradley Garcia', 41, 'Malawi', 'Pilot', None, None, 19, None), (422, 'Matthew Ross', 26, 'Russian Federation', 'Gym Trainer', None, 'Commercial License', 14, 'English Fluent'), (423, 'Nancy Franklin', 58, 'Algeria', 'Office Driver', None, \"Bachelor's Degree\", 12, 'French Basic'), (424, 'Jose Bradley', 40, 'Bahrain', 'Social Media Influencer', None, None, 20, None), (425, 'Ryan Avila', 47, 'Samoa', 'Engineer', None, None, 12, 'Chinese Fluent'), (426, 'Steven Ho', 28, 'Serbia', 'Professor', None, 'Diploma', 6, 'Spanish Intermediate'), (427, 'Jennifer Trevino', 47, 'Mali', 'Singer', None, 'B Tech', 9, None), (428, 'Amy Burns', 32, 'Botswana', 'Data Scientist', 'Data Visualization', 'PHD', 2, 'Spanish Fluent'), (429, 'Ashley Smith', 57, 'Saint Kitts and Nevis', 'Truck Driver', 'Tanker, OTR', 'Diploma', 14, 'German C1'), (430, 'Melissa Jones', 23, 'Somalia', 'Movie Producer', None, 'Associate Degree', 16, None), (431, 'Casey Flores', 31, 'Fiji', 'Movie Director', None, 'PHD', 11, 'French Basic'), (432, 'Jonathan Robertson', 36, 'Argentina', 'Teacher', None, 'Diploma', 15, 'French Intermediate'), (433, 'Natalie Torres', 25, 'Aruba', 'Product Manager', None, \"Master's Degree\", 5, None), (434, 'Tyler Hall', 31, 'Maldives', 'Nurse', 'ICU, Long-Term Care, Wound Care, Emergency, Surgery', 'M Tech', 3, 'German C2'), (435, 'Michelle Mccoy', 25, 'Marshall Islands', 'Pilot', None, None, 19, None), (436, 'Ashley White', 39, 'Comoros', 'Painter', None, 'Associate Degree', 20, 'French Basic'), (437, 'Tyler Weeks', 56, 'Iceland', 'UX/UI Designer', 'Usability Testing, Prototyping, User Research, Wireframing', None, 16, 'German B2'), (438, 'Ryan Walker', 58, 'Iran', 'Singer', None, 'Associate Degree', 18, 'English Fluent'), (439, 'Mrs. Samantha Hicks', 54, 'American Samoa', 'Social Media Influencer', None, \"Master's Degree\", 20, 'English Fluent'), (440, 'Pamela Smith', 24, 'Anguilla', 'Software Developer', 'JavaScript, C++, Agile Development, Python, Java', 'High School', 10, 'Chinese Fluent'), (441, 'Karen Anderson', 51, 'Zimbabwe', 'Cricketer', None, 'M Tech', 5, 'English Intermediate'), (442, 'Claudia Ward', 44, 'Papua New Guinea', 'Office Driver', None, 'Commercial License', 7, 'English Basic'), (443, 'David Hunter', 24, 'Hungary', 'Engineer', 'Mechanical Engineering, Civil Engineering, Chemical Engineering, Electrical Engineering', 'Associate Degree', 2, 'Chinese Intermediate'), (444, 'Chelsea Ross', 51, 'Northern Mariana Islands', 'Licensed Practical Nurse', 'Long-Term Care', None, 13, 'French Basic'), (445, 'Daniel Gill', 47, 'Greenland', 'Movie Producer', None, 'High School', 3, 'Chinese Fluent'), (446, 'Diana Williams', 23, 'Cyprus', 'Engineer', 'Chemical Engineering', \"Bachelor's Degree\", 14, None), (447, 'Tammy Santana', 52, 'Saint Helena', 'Cybersecurity Specialist', 'Incident Response, Network Security, Compliance, Threat Analysis', 'M Tech', 9, None), (448, 'Jerry Randolph', 28, 'Wallis and Futuna', 'Software Developer', 'Python, JavaScript, Java, C++', None, 14, None), (449, 'Steve Weiss', 45, 'Sierra Leone', 'Movie Director', None, 'M Tech', 3, 'Spanish Intermediate'), (450, 'Amanda Jones', 56, 'Liberia', 'Movie Director', None, 'BSc Nursing', 11, 'English Fluent'), (451, 'Patrick Thompson', 44, 'Papua New Guinea', 'Software Developer', 'C++, Agile Development', 'BSc Nursing', 15, 'English Basic'), (452, 'Stephen Robertson', 34, 'Reunion', 'Teacher', None, 'BSc Nursing', 11, 'French Fluent'), (453, 'Kimberly Hansen', 58, 'French Guiana', 'AI Engineer', None, 'PHD', 6, 'German B2'), (454, 'Jeremy Reynolds', 51, 'Georgia', 'Product Manager', 'Product Development, Project Management', \"Master's Degree\", 2, None), (455, 'Mr. John Reed', 36, 'Korea', 'Data Scientist', None, 'Diploma', 19, 'English Fluent'), (456, 'David Allen', 55, 'Solomon Islands', 'Teacher', None, 'B Tech', 5, 'English Intermediate'), (457, 'Jonathan Dennis', 48, 'Haiti', 'Network Administrator', 'Network Design', 'BSc Nursing', 6, 'French Fluent'), (458, 'Jonathan Ortega', 51, 'Ghana', 'DevOps Engineer', None, \"Bachelor's Degree\", 1, 'French Basic'), (459, 'Joseph Collins', 51, 'Tanzania', 'Product Manager', None, \"Bachelor's Degree\", 1, 'Chinese Basic'), (460, 'Amy Buchanan', 51, 'Vanuatu', 'School Bus Driver', None, 'Commercial License', 3, None), (461, 'Trevor Anderson', 28, 'Jamaica', 'Gym Trainer', None, 'PHD', 3, 'Chinese Fluent'), (462, 'Joshua Roman', 50, 'Eritrea', 'Truck Driver', 'Defensive Driving, OTR, Hazmat', 'Diploma', 20, 'Spanish Fluent'), (463, 'Stephanie Shah', 57, 'New Zealand', 'Cybersecurity Specialist', 'Incident Response, Compliance, Threat Analysis, Network Security', 'B Tech', 5, 'French Basic'), (464, 'Rachel Carr', 26, 'Guam', 'Network Administrator', 'Network Design', None, 1, 'Spanish Intermediate'), (465, 'Daniel Ford', 41, 'South Africa', 'AI Engineer', None, None, 10, 'Spanish Fluent'), (466, 'William Wright', 49, 'Kuwait', 'Social Media Influencer', None, 'PHD', 16, None), (467, 'George Patterson', 50, 'Guyana', 'Professor', None, None, 20, None), (468, 'Jonathan Thomas', 29, 'Sri Lanka', 'Gym Trainer', None, 'Commercial License', 11, 'Chinese Intermediate'), (469, 'Miss Sheila Brewer', 44, 'Burkina Faso', 'Software Developer', 'JavaScript, Java', 'Commercial License', 13, 'Chinese Basic'), (470, 'Joy Castillo', 29, 'Austria', 'Test Engineer', None, 'PHD', 1, 'French Basic'), (471, 'Patrick Smith', 25, 'Mozambique', 'Network Administrator', 'Network Security, Network Design, Troubleshooting', None, 7, 'English Fluent'), (472, 'Crystal Thompson', 41, 'Portugal', 'Nurse', None, 'Diploma', 14, None), (473, 'James Martinez', 26, 'Brunei Darussalam', 'Painter', None, 'Diploma', 13, None), (474, 'Jillian Little', 45, 'Botswana', 'Truck Driver', 'Tanker, Defensive Driving, Hazmat', 'Certificate Program', 18, 'French Fluent'), (475, 'Timothy Johnson', 25, 'Vanuatu', 'Gym Trainer', None, 'Certificate Program', 6, 'English Fluent'), (476, 'Robert Brown Jr.', 41, 'Solomon Islands', 'Painter', None, 'B Tech', 18, 'Chinese Fluent'), (477, 'Brenda Calhoun', 51, 'Bolivia', 'Pilot', None, \"Master's Degree\", 5, 'German C2'), (478, 'Timothy Dyer', 22, 'Bangladesh', 'Painter', None, None, 4, 'German A2'), (479, 'Crystal Andrews', 46, 'Gambia', 'UX/UI Designer', 'Prototyping, Wireframing, User Research', 'B Tech', 12, 'German A1'), (480, 'William Cain', 48, 'British Indian Ocean Territory (Chagos Archipelago)', 'Office Driver', None, None, 20, 'German C1'), (481, 'Stuart Brooks', 27, 'Turkey', 'Database Administrator', 'Database Design, Performance Tuning', \"Bachelor's Degree\", 16, 'Spanish Intermediate'), (482, 'Kellie Garcia', 44, 'Monaco', 'Singer', None, 'B Tech', 20, 'Chinese Intermediate'), (483, 'Joseph Phillips', 32, 'Qatar', 'Network Administrator', 'Network Design, Network Security', 'BSc Nursing', 4, 'Chinese Basic'), (484, 'Wendy Smith', 45, 'Mongolia', 'Cricketer', None, 'PHD', 1, 'English Fluent'), (485, 'Maria Burns', 24, 'Papua New Guinea', 'Truck Driver', 'OTR', None, 9, 'Chinese Fluent'), (486, 'Robert Powell', 43, 'Bulgaria', 'Teacher', None, 'B Tech', 20, 'French Basic'), (487, 'Anna Flowers', 42, 'Croatia', 'Movie Director', None, \"Master's Degree\", 15, 'Chinese Basic'), (488, 'Pamela Taylor', 44, 'China', 'Singer', None, 'PHD', 19, 'Spanish Intermediate'), (489, 'Lindsay Jenkins', 55, 'Jamaica', 'Truck Driver', 'Tanker, Hazmat', 'B Tech', 17, 'French Fluent'), (490, 'Robert Stevenson', 51, 'India', 'Dancer', None, \"Bachelor's Degree\", 19, None), (491, 'Jason Knight', 44, 'Portugal', 'Data Scientist', 'Data Analysis', \"Master's Degree\", 6, 'French Intermediate'), (492, 'Matthew Miller', 47, 'Bosnia and Herzegovina', 'Singer', None, 'Associate Degree', 10, 'Chinese Fluent'), (493, 'David Harrell', 47, 'Canada', 'Professor', None, 'Diploma', 16, 'French Intermediate'), (494, 'Jonathan Mcgrath', 41, 'Kuwait', 'School Bus Driver', 'Passenger Endorsement', 'Commercial License', 11, 'Spanish Fluent'), (495, 'Alison Johnson', 27, 'Russian Federation', 'Singer', None, None, 15, None), (496, 'Kimberly Williams', 33, 'Eritrea', 'Athelete', None, \"Master's Degree\", 17, None), (497, 'Katie Green', 46, 'Belize', 'Office Driver', None, None, 1, 'Chinese Basic'), (498, 'Dean Gallegos', 46, 'Switzerland', 'DevOps Engineer', 'CI/CD', 'BSc Nursing', 15, 'Spanish Basic'), (499, 'James Wade', 26, 'Albania', 'Database Administrator', None, None, 20, None), (500, 'Robert Murphy', 39, 'Kiribati', 'Social Media Influencer', None, None, 10, 'English Basic')]\n"]}, {"ename": "ValueError", "evalue": "DataFrame constructor not properly called!", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[32m/tmp/ipykernel_36736/**********.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m      1\u001b[39m query_result = db.run(\u001b[33m\"SELECT * FROM candidates ;\"\u001b[39m)\n\u001b[32m      2\u001b[39m print(\u001b[33m\"Query result from candidates table: \\n\"\u001b[39m, query_result)\n\u001b[32m      3\u001b[39m \n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# print result in a df\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m df = pd.DataFrame(query_result)\n", "\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, data, index, columns, dtype, copy)\u001b[39m\n\u001b[32m    882\u001b[39m                 )\n\u001b[32m    883\u001b[39m         \u001b[38;5;66;03m# For data is scalar\u001b[39;00m\n\u001b[32m    884\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    885\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m index \u001b[38;5;28;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mor\u001b[39;00m columns \u001b[38;5;28;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m886\u001b[39m                 \u001b[38;5;28;01mraise\u001b[39;00m ValueError(\u001b[33m\"DataFrame constructor not properly called!\"\u001b[39m)\n\u001b[32m    887\u001b[39m \n\u001b[32m    888\u001b[39m             index = ensure_index(index)\n\u001b[32m    889\u001b[39m             columns = ensure_index(columns)\n", "\u001b[31mValueError\u001b[39m: DataFrame constructor not properly called!"]}], "source": ["query_result = db.run(\"SELECT * FROM candidates ;\")\n", "print(\"Query result from candidates table: \\n\", query_result)\n", "\n", "# print result in a df\n", "df = pd.DataFrame(query_result)"]}, {"cell_type": "code", "execution_count": null, "id": "46137ca1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "29bc990f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4e65b8d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4d0b87cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "00d66a2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "cd465087", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "349470c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e031af63", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "6b616f77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c9f26ec8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "5bffb436", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c109cd55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "1185eaa6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "02084ebe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "dee2715f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"profession\": \"school bus driver\",\n", "  \"skills\": [\n", "    \"passenger endorsement\"\n", "  ],\n", "  \"experience_years\": 5,\n", "  \"education\": null,\n", "  \"languages\": null\n", "}\n"]}], "source": ["import os\n", "from typing import List, Optional\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level in the language (e.g., B2, C1)\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"The profession or job title mentioned\")\n", "    skills: Optional[List[str]] = Field(None, description=\"List of skills required for the job\")\n", "    experience_years: Optional[int] = Field(None, description=\"Years of experience required\")\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"List of languages and their proficiency levels\")\n", "\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "# print(f\"key is: {api_key}\")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a recruitment assistant that extracts key fields from employer queries.\"),\n", "    (\"user\", \"{query}\\n\\n{format_instructions}\")\n", "])\n", "\n", "\n", "def extract_structured_query(query: str) -> ParsedQuery:\n", "    formatted_prompt = prompt.format_messages(\n", "        query=query,\n", "        format_instructions=format_instructions\n", "    )\n", "\n", "    response = llm.invoke(formatted_prompt)  # ✅ use invoke\n", "    parsed = parser.parse(response.content)\n", "    return parsed\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Find me school bus driver with passenger endorsement having 5+ years of experience\"\n", "    result = extract_structured_query(query)\n", "    # print(result)\n", "    print(result.model_dump_json(indent=2))  # or result.dict()\n"]}, {"cell_type": "code", "execution_count": null, "id": "86c34db1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}