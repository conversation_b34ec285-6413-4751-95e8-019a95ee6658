{"cells": [{"cell_type": "code", "execution_count": 27, "id": "b2d4746a", "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Dict, List, Optional\n", "\n", "from openai import OpenAI\n", "from pydantic import BaseModel\n", "from sqlalchemy import Column, Integer, String, Text, create_engine\n", "from sqlalchemy.orm import declarative_base, sessionmaker"]}, {"cell_type": "code", "execution_count": 28, "id": "63fff1f3", "metadata": {}, "outputs": [], "source": ["# 2. Set your OpenAI API key\n", "api_key = \"********************************************************************************************************************************************************************\"  # Replace with your actual key"]}, {"cell_type": "code", "execution_count": 29, "id": "f8ad5cec", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key=api_key)"]}, {"cell_type": "code", "execution_count": 30, "id": "174c1320", "metadata": {}, "outputs": [], "source": ["# 3. Pydantic model for extracted query\n", "class SearchQuery(BaseModel):\n", "    role: str\n", "    specialization: Optional[str] = None\n", "    experience_years: Optional[int] = None\n", "    language: Optional[str] = None\n", "    language_proficiency: Optional[str] = None\n", "    certifications: Optional[List[str]] = []\n", "    endorsements: Optional[List[str]] = []\n", "    licenses: Optional[List[str]] = []"]}, {"cell_type": "code", "execution_count": 32, "id": "b6708922", "metadata": {}, "outputs": [], "source": ["# 4. SQLAlchemy setup\n", "Base = declarative_base()\n", "engine = create_engine(\"sqlite:///:memory:\")\n", "Session = sessionmaker(bind=engine)\n", "session = Session()"]}, {"cell_type": "code", "execution_count": 33, "id": "1b8e3e3a", "metadata": {}, "outputs": [], "source": ["class Candidate(Base):\n", "    __tablename__ = \"candidates\"\n", "    id = Column(Integer, primary_key=True)\n", "    name = Column(String)\n", "    profession = Column(String)\n", "    experience_years = Column(Integer)\n", "    skills = Column(Text)\n", "    education = Column(String)\n", "    languages = Column(Text)\n", "    certifications = Column(Text)\n", "    endorsements = Column(Text)\n", "    licenses = Column(Text)"]}, {"cell_type": "code", "execution_count": 34, "id": "3cc90a3a", "metadata": {}, "outputs": [], "source": ["Base.metadata.create_all(engine)"]}, {"cell_type": "code", "execution_count": 35, "id": "37ea2d5a", "metadata": {}, "outputs": [], "source": ["# 5. <PERSON><PERSON> data\n", "mock_candidates = [\n", "    Candidate(\n", "        name=\"<PERSON>\",\n", "        profession=\"Nurse\",\n", "        experience_years=6,\n", "        skills=json.dumps([\"ICU\"]),\n", "        education=\"BSc Nursing\",\n", "        languages=json.dumps([{\"language\": \"German\", \"proficiency\": \"B2\"}]),\n", "        certifications=json.dumps([]),\n", "        endorsements=json.dumps([]),\n", "        licenses=json.dumps([])\n", "    ),\n", "    Candidate(\n", "        name=\"<PERSON>\",\n", "        profession=\"Truck Driver\",\n", "        experience_years=3,\n", "        skills=json.dumps([\"OTR\"]),\n", "        education=\"High School\",\n", "        languages=json.dumps([{\"language\": \"English\", \"proficiency\": \"C1\"}]),\n", "        certifications=json.dumps([\"CDL Class A\"]),\n", "        endorsements=json.dumps([\"Tanker\"]),\n", "        licenses=json.dumps([\"Hazmat\"])\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": 36, "id": "c29feab7", "metadata": {}, "outputs": [], "source": ["session.add_all(mock_candidates)\n", "session.commit()"]}, {"cell_type": "code", "execution_count": 37, "id": "519a8820", "metadata": {}, "outputs": [], "source": ["# 6. Function schema for OpenAI function calling\n", "function_schema = {\n", "    \"name\": \"extract_candidate_filters\",\n", "    \"description\": \"Extracts structured filters for searching candidates from a natural language query.\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"role\": {\"type\": \"string\"},\n", "            \"specialization\": {\"type\": \"string\"},\n", "            \"experience_years\": {\"type\": \"integer\"},\n", "            \"language\": {\"type\": \"string\"},\n", "            \"language_proficiency\": {\"type\": \"string\"},\n", "            \"certifications\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n", "            \"endorsements\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n", "            \"licenses\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}}\n", "        },\n", "        \"required\": [\"role\"]\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "92c83132", "metadata": {}, "outputs": [], "source": ["# 7. Run query through OpenAI to extract structured filters\n", "user_query = \"Find me nurses with 5 years experience in ICU with German B2 proficiency\""]}, {"cell_type": "code", "execution_count": 39, "id": "c55146d8", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"gpt-4o\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You extract structured filters for searching candidate records.\"},\n", "        {\"role\": \"user\", \"content\": user_query}\n", "    ],\n", "    functions=[function_schema],\n", "    function_call={\"name\": \"extract_candidate_filters\"}\n", ")"]}, {"cell_type": "code", "execution_count": 41, "id": "ff7955b3", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatCompletion(id='chatcmpl-Bo6SWb1FOOAhjZBhAx54x16Og6OcN', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content=None, refusal=None, role='assistant', annotations=[], audio=None, function_call=FunctionCall(arguments='{\"role\":\"nurse\",\"specialization\":\"ICU\",\"experience_years\":5,\"language\":\"German\",\"language_proficiency\":\"B2\"}', name='extract_candidate_filters'), tool_calls=None))], created=1751280928, model='gpt-4o-2024-08-06', object='chat.completion', service_tier='default', system_fingerprint='fp_07871e2ad8', usage=CompletionUsage(completion_tokens=29, prompt_tokens=118, total_tokens=147, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 58, "id": "80b28d21", "metadata": {}, "outputs": [], "source": ["# function_args = json.loads(response.choices[0][\"message\"][\"function_call\"][\"arguments\"])\n", "# search_input = SearchQuery(**function_args)\n", "\n", "\n", "arguments_str = response.choices[0].message.function_call.arguments\n", "function_args = json.loads(arguments_str)\n", "search_input = SearchQuery(**function_args)"]}, {"cell_type": "code", "execution_count": 59, "id": "10e528db", "metadata": {}, "outputs": [], "source": ["# 8. Matching logic\n", "def compute_match_score(candidate: Candidate, filters: SearchQuery) -> float:\n", "    score = 0\n", "    weight = {\n", "        \"role\": 30,\n", "        \"specialization\": 20,\n", "        \"experience_years\": 20,\n", "        \"language_proficiency\": 15\n", "    }\n", "\n", "    if filters.role.lower() in candidate.profession.lower():\n", "        score += weight[\"role\"]\n", "    skills = json.loads(candidate.skills)\n", "    if filters.specialization and filters.specialization.lower() in [s.lower() for s in skills]:\n", "        score += weight[\"specialization\"]\n", "    if filters.experience_years and candidate.experience_years >= filters.experience_years:\n", "        score += weight[\"experience_years\"]\n", "    langs = json.loads(candidate.languages)\n", "    for lang in langs:\n", "        if (lang[\"language\"].lower() == filters.language.lower() and \n", "            lang[\"proficiency\"].upper() == filters.language_proficiency.upper()):\n", "            score += weight[\"language_proficiency\"]\n", "    return round(score, 1)\n"]}, {"cell_type": "code", "execution_count": 60, "id": "3912e874", "metadata": {}, "outputs": [], "source": ["# 9. Filter and return results\n", "def search_candidates(filters: SearchQuery) -> List[Dict]:\n", "    candidates = session.query(Candidate).all()\n", "    results = []\n", "\n", "    for c in candidates:\n", "        score = compute_match_score(c, filters)\n", "        if score > 0:\n", "            results.append({\n", "                \"name\": c.name,\n", "                \"profession\": c.profession,\n", "                \"experience_years\": c.experience_years,\n", "                \"skills\": json.loads(c.skills),\n", "                \"education\": c.education,\n", "                \"languages\": json.loads(c.languages),\n", "                \"match_score\": score\n", "            })\n", "\n", "    return sorted(results, key=lambda x: x[\"match_score\"], reverse=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ed92caaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"name\": \"<PERSON>\",\n", "    \"profession\": \"Nurse\",\n", "    \"experience_years\": 6,\n", "    \"skills\": [\n", "      \"ICU\"\n", "    ],\n", "    \"education\": \"BSc Nursing\",\n", "    \"languages\": [\n", "      {\n", "        \"language\": \"German\",\n", "        \"proficiency\": \"B2\"\n", "      }\n", "    ],\n", "    \"match_score\": 85\n", "  }\n", "]\n"]}], "source": ["matched_json = json.dumps(search_candidates(search_input), indent=2, ensure_ascii=False)\n", "print(matched_json)"]}, {"cell_type": "code", "execution_count": null, "id": "08178fdf", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "import json\n", "\n", "# Define your database connection parameters\n", "database_path = \"candidates.db\"\n", "table_name = \"candidates\""]}, {"cell_type": "code", "execution_count": null, "id": "2fe42dc3", "metadata": {}, "outputs": [], "source": ["# Connect to the SQLite database and read data\n", "def read_from_sqlite(db_path, table_name):\n", "    try:\n", "        conn = sqlite3.connect(db_path)\n", "        df = pd.read_sql_query(f\"SELECT * FROM {table_name}\", conn)\n", "        conn.close()\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Failed to read from database: {e}\")\n", "        return None\n", "\n", "\n", "# Convert DataFrame rows to JSON and print\n", "def print_rows_as_json(df):\n", "    if df is not None:\n", "        for index, row in df.iterrows():\n", "            # Convert the row to a dictionary and then to JSON\n", "            record_json = json.dumps(row.to_dict())\n", "            print(record_json)\n", "    else:\n", "        print(\"No data to print.\")"]}, {"cell_type": "code", "execution_count": 3, "id": "5f2ae91c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"id\": 1, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 11, \"skills\": \"[\\\"Python\\\"]\", \"education\": \"Diploma\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[]\", \"licenses\": \"[]\"}\n", "{\"id\": 2, \"name\": \"<PERSON>\", \"profession\": \"Electrician\", \"experience_years\": 6, \"skills\": \"[\\\"Commercial\\\"]\", \"education\": \"MBBS\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"A2\\\"}]\", \"certifications\": \"[\\\"PALS\\\"]\", \"endorsements\": \"[\\\"Doubles\\\", \\\"Tanker\\\"]\", \"licenses\": \"[\\\"Hazmat\\\", \\\"Medical License\\\"]\"}\n", "{\"id\": 3, \"name\": \"<PERSON>\", \"profession\": \"Nurse\", \"experience_years\": 10, \"skills\": \"[\\\"Pediatrics\\\"]\", \"education\": \"High School\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Tanker\\\"]\", \"licenses\": \"[]\"}\n", "{\"id\": 4, \"name\": \"<PERSON>\", \"profession\": \"Electrician\", \"experience_years\": 6, \"skills\": \"[\\\"Residential\\\"]\", \"education\": \"Diploma\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"A2\\\"}]\", \"certifications\": \"[\\\"ACLS\\\"]\", \"endorsements\": \"[\\\"Doubles\\\"]\", \"licenses\": \"[\\\"Hazmat\\\", \\\"Medical License\\\"]\"}\n", "{\"id\": 5, \"name\": \"<PERSON>\", \"profession\": \"Truck Driver\", \"experience_years\": 14, \"skills\": \"[\\\"Hazmat\\\"]\", \"education\": \"BTech\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"C2\\\"}]\", \"certifications\": \"[\\\"ACLS\\\"]\", \"endorsements\": \"[\\\"Tanker\\\"]\", \"licenses\": \"[]\"}\n", "{\"id\": 6, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 5, \"skills\": \"[\\\"Java\\\"]\", \"education\": \"BTech\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"A2\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Tanker\\\"]\", \"licenses\": \"[\\\"Medical License\\\"]\"}\n", "{\"id\": 7, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 3, \"skills\": \"[\\\"React\\\"]\", \"education\": \"BTech\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"C1\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Triples\\\"]\", \"licenses\": \"[]\"}\n", "{\"id\": 8, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 7, \"skills\": \"[\\\"Python\\\"]\", \"education\": \"BSc Nursing\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[\\\"PALS\\\"]\", \"endorsements\": \"[\\\"Doubles\\\"]\", \"licenses\": \"[\\\"Electrical License\\\", \\\"Medical License\\\"]\"}\n", "{\"id\": 9, \"name\": \"<PERSON>\", \"profession\": \"Truck Driver\", \"experience_years\": 8, \"skills\": \"[\\\"OTR\\\"]\", \"education\": \"BSc Nursing\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[\\\"BLS\\\"]\", \"endorsements\": \"[\\\"Triples\\\"]\", \"licenses\": \"[\\\"Medical License\\\"]\"}\n", "{\"id\": 10, \"name\": \"James Hall\", \"profession\": \"Electrician\", \"experience_years\": 4, \"skills\": \"[\\\"Commercial\\\"]\", \"education\": \"BTech\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[\\\"PALS\\\", \\\"ACLS\\\"]\", \"endorsements\": \"[]\", \"licenses\": \"[]\"}\n", "{\"id\": 11, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 5, \"skills\": \"[\\\"React\\\"]\", \"education\": \"High School\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Tanker\\\"]\", \"licenses\": \"[\\\"Medical License\\\", \\\"Hazmat\\\"]\"}\n", "{\"id\": 12, \"name\": \"<PERSON>\", \"profession\": \"Nurse\", \"experience_years\": 15, \"skills\": \"[\\\"Pediatrics\\\"]\", \"education\": \"Diploma\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[\\\"BLS\\\", \\\"CDL Class A\\\"]\", \"endorsements\": \"[]\", \"licenses\": \"[\\\"Electrical License\\\"]\"}\n", "{\"id\": 13, \"name\": \"<PERSON>\", \"profession\": \"Truck Driver\", \"experience_years\": 3, \"skills\": \"[\\\"Hazmat\\\"]\", \"education\": \"MBBS\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"C1\\\"}]\", \"certifications\": \"[\\\"AWS Certified\\\"]\", \"endorsements\": \"[]\", \"licenses\": \"[\\\"Medical License\\\"]\"}\n", "{\"id\": 14, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 7, \"skills\": \"[\\\"Python\\\"]\", \"education\": \"High School\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Doubles\\\"]\", \"licenses\": \"[\\\"Electrical License\\\"]\"}\n", "{\"id\": 15, \"name\": \"<PERSON>\", \"profession\": \"Nurse\", \"experience_years\": 2, \"skills\": \"[\\\"ICU\\\"]\", \"education\": \"MBBS\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Doubles\\\", \\\"Triples\\\"]\", \"licenses\": \"[\\\"Medical License\\\"]\"}\n", "{\"id\": 16, \"name\": \"<PERSON>\", \"profession\": \"Electrician\", \"experience_years\": 9, \"skills\": \"[\\\"Commercial\\\"]\", \"education\": \"MBBS\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[\\\"AWS Certified\\\", \\\"BLS\\\"]\", \"endorsements\": \"[]\", \"licenses\": \"[\\\"Electrical License\\\", \\\"Hazmat\\\"]\"}\n", "{\"id\": 17, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 2, \"skills\": \"[\\\"Java\\\"]\", \"education\": \"BTech\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[\\\"PALS\\\", \\\"BLS\\\"]\", \"endorsements\": \"[\\\"Triples\\\"]\", \"licenses\": \"[\\\"Electrical License\\\"]\"}\n", "{\"id\": 18, \"name\": \"<PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 10, \"skills\": \"[\\\"Python\\\"]\", \"education\": \"Diploma\", \"languages\": \"[{\\\"language\\\": \\\"German\\\", \\\"proficiency\\\": \\\"B2\\\"}]\", \"certifications\": \"[\\\"CDL Class A\\\"]\", \"endorsements\": \"[]\", \"licenses\": \"[]\"}\n", "{\"id\": 19, \"name\": \"<PERSON><PERSON>\", \"profession\": \"Software Engineer\", \"experience_years\": 15, \"skills\": \"[\\\"React\\\"]\", \"education\": \"Diploma\", \"languages\": \"[{\\\"language\\\": \\\"English\\\", \\\"proficiency\\\": \\\"B1\\\"}]\", \"certifications\": \"[]\", \"endorsements\": \"[\\\"Tanker\\\", \\\"Doubles\\\"]\", \"licenses\": \"[\\\"Medical License\\\"]\"}\n", "{\"id\": 20, \"name\": \"<PERSON>\", \"profession\": \"Nurse\", \"experience_years\": 1, \"skills\": \"[\\\"Pediatrics\\\"]\", \"education\": \"High School\", \"languages\": \"[{\\\"language\\\": \\\"Spanish\\\", \\\"proficiency\\\": \\\"A2\\\"}]\", \"certifications\": \"[\\\"AWS Certified\\\"]\", \"endorsements\": \"[\\\"Doubles\\\"]\", \"licenses\": \"[\\\"Hazmat\\\", \\\"Medical License\\\"]\"}\n"]}], "source": ["# Example usage\n", "if __name__ == \"__main__\":\n", "    df = read_from_sqlite(database_path, table_name)\n", "    print_rows_as_json(df)"]}, {"cell_type": "markdown", "id": "df4cd1e2", "metadata": {}, "source": ["### dummy 50 records"]}, {"cell_type": "code", "execution_count": 2, "id": "2ed0f69f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Candidates inserted into SQLite with some missing values using Faker.\n"]}], "source": ["import random\n", "import sqlite3\n", "\n", "from faker import Faker\n", "\n", "fake = Faker()\n", "\n", "# Sample pools\n", "professions = [\n", "    \"Cricketer\",\n", "    \"Pilot\",\n", "    \"Engineer\",\n", "    \"Data Scientist\",\n", "    \"AI Engineer\",\n", "    \"Test Engineer\",\n", "    \"Gym Trainer\",\n", "    \"Teacher\",\n", "    \"Professor\",\n", "    \"Athelete\",\n", "    \"Social Media Influencer\",\n", "    \"Painter\",\n", "    \"Office Driver\",\n", "    \"Singer\",\n", "    \"Movie Director\",\n", "    \"Movie Producer\",\n", "    \"Dancer\",\n", "    \"Nurse\",\n", "    \"Truck Driver\",\n", "    \"School Bus Driver\",\n", "    \"Licensed Practical Nurse\",\n", "]\n", "\n", "skills_map = {\n", "    \"Nurse\": [\"ICU\", \"Pediatrics\", \"Emergency\", \"Surgery\"],\n", "    \"Truck Driver\": [\"<PERSON><PERSON>\", \"<PERSON><PERSON>mat\", \"Tanker\"],\n", "    \"School Bus Driver\": [\"Passenger Endorsement\"],\n", "    \"Licensed Practical Nurse\": [\"Wound Care\", \"Long-Term Care\"],\n", "}\n", "education_levels = [\n", "    \"High School\",\n", "    \"Diploma\",\n", "    \"BSc Nursing\",\n", "    \"Associate Degree\",\n", "    \"Commercial License\",\n", "    \"M Tech\",\n", "    \"B Tech\",\n", "    \"PHD\",\n", "]\n", "languages = [\n", "    (\"German\", [\"A1\", \"A2\", \"B1\", \"B2\", \"C1\", \"C2\"]),\n", "    (\"English\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "    (\"Spanish\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "]\n", "\n", "# Connect to SQLite\n", "conn = sqlite3.connect(\"candidates_details.db\")\n", "cursor = conn.cursor()\n", "\n", "# Create table\n", "cursor.execute(\n", "    \"\"\"\n", "CREATE TABLE IF NOT EXISTS candidates (\n", "    id INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    name TEXT,\n", "    age INTEGER,\n", "    country TEXT,\n", "    profession TEXT,\n", "    skills TEXT,\n", "    education TEXT,\n", "    experience_years INTEGER,\n", "    language TEXT\n", ")\n", "\"\"\"\n", ")\n", "\n", "# Insert 500 records\n", "for _ in range(50):\n", "    name = fake.name()\n", "    age = random.randint(22, 60)\n", "    country = fake.country()\n", "    profession = random.choice(professions)\n", "\n", "    # Randomly skip 30% of skills\n", "    skills = (\n", "        \", \".join(\n", "            random.sample(\n", "                skills_map[profession], random.randint(1, len(skills_map[profession]))\n", "            )\n", "        )\n", "        if random.random() > 0.3\n", "        else None\n", "    )\n", "\n", "    # Randomly skip 20% of education\n", "    education = random.choice(education_levels) if random.random() > 0.2 else None\n", "\n", "    experience = random.randint(1, 20)\n", "\n", "    # Randomly skip 25% of language\n", "    if random.random() > 0.25:\n", "        lang, levels = random.choice(languages)\n", "        language = f\"{lang} {random.choice(levels)}\"\n", "    else:\n", "        language = None\n", "\n", "    # Insert into table\n", "    cursor.execute(\n", "        \"\"\"\n", "    INSERT INTO candidates (name, age, country, profession, skills, education, experience_years, language)\n", "    VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n", "    \"\"\",\n", "        (name, age, country, profession, skills, education, experience, language),\n", "    )\n", "\n", "conn.commit()\n", "conn.close()\n", "\n", "print(\"✅ Candidates inserted into SQLite with some missing values using Faker.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "15a729ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.3 environment at: /home/<USER>/uv_environments/testing\u001b[0m\n", "\u001b[2K\u001b[2mResolved \u001b[1m2 packages\u001b[0m \u001b[2min 560ms\u001b[0m\u001b[0m                                         \u001b[0m\n", "\u001b[2K\u001b[2mInstalled \u001b[1m1 package\u001b[0m \u001b[2min 72ms\u001b[0m\u001b[0m                                 \u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mfaker\u001b[0m\u001b[2m==37.4.0\u001b[0m\n"]}], "source": ["!uv pip install faker"]}, {"cell_type": "code", "execution_count": null, "id": "484c4bab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Candidates inserted into SQLite with some missing values using Faker.\n"]}], "source": ["import random\n", "import sqlite3\n", "from faker import Faker\n", "\n", "fake = Faker()\n", "\n", "# Sample pools\n", "professions = [\n", "    \"Cricketer\",\n", "    \"Pilot\",\n", "    \"Engineer\",\n", "    \"Data Scientist\",\n", "    \"AI Engineer\",\n", "    \"Test Engineer\",\n", "    \"Gym Trainer\",\n", "    \"Teacher\",\n", "    \"Professor\",\n", "    \"Athelete\",\n", "    \"Social Media Influencer\",\n", "    \"Painter\",\n", "    \"Office Driver\",\n", "    \"Singer\",\n", "    \"Movie Director\",\n", "    \"Movie Producer\",\n", "    \"Dancer\",\n", "    \"Nurse\",\n", "    \"Truck Driver\",\n", "    \"School Bus Driver\",\n", "    \"Licensed Practical Nurse\",\n", "    \"Software Developer\",\n", "    \"DevOps Engineer\",\n", "    \"Cybersecurity Specialist\",\n", "    \"Network Administrator\",\n", "    \"Database Administrator\",\n", "    \"UX/UI Designer\",\n", "    \"Product Manager\",\n", "]\n", "\n", "skills_map = {\n", "    \"Nurse\": [\n", "        \"ICU\",\n", "        \"Pediatrics\",\n", "        \"Emergency\",\n", "        \"Surgery\",\n", "        \"Wound Care\",\n", "        \"Long-Term Care\",\n", "    ],\n", "    \"Truck Driver\": [\"<PERSON>TR\", \"Hazmat\", \"Tanker\", \"Defensive Driving\"],\n", "    \"School Bus Driver\": [\"Passenger Endorsement\", \"Defensive Driving\"],\n", "    \"Licensed Practical Nurse\": [\n", "        \"Wound Care\",\n", "        \"Long-Term Care\",\n", "        \"Medication Administration\",\n", "    ],\n", "    \"Software Developer\": [\"Python\", \"Java\", \"JavaScript\", \"C++\", \"Agile Development\"],\n", "    \"DevOps Engineer\": [\"CI/CD\", \"Containerization\", \"Cloud Computing\", \"Monitoring\"],\n", "    \"Cybersecurity Specialist\": [\n", "        \"Network Security\",\n", "        \"Threat Analysis\",\n", "        \"Incident Response\",\n", "        \"Compliance\",\n", "    ],\n", "    \"Network Administrator\": [\"Network Design\", \"Network Security\", \"Troubleshooting\"],\n", "    \"Database Administrator\": [\n", "        \"Database Design\",\n", "        \"Performance Tuning\",\n", "        \"Backup and Recovery\",\n", "    ],\n", "    \"UX/UI Designer\": [\n", "        \"User Research\",\n", "        \"Wireframing\",\n", "        \"Prototyping\",\n", "        \"Usability Testing\",\n", "    ],\n", "    \"Product Manager\": [\"Product Development\", \"Market Research\", \"Project Management\"],\n", "    \"Engineer\": [\n", "        \"Mechanical Engineering\",\n", "        \"Electrical Engineering\",\n", "        \"Civil Engineering\",\n", "        \"Chemical Engineering\",\n", "    ],\n", "    \"Data Scientist\": [\n", "        \"Machine Learning\",\n", "        \"Data Analysis\",\n", "        \"Data Visualization\",\n", "        \"Statistics\",\n", "    ],\n", "    \"AI Engineer\": [\n", "        \"Natural Language Processing\",\n", "        \"Computer Vision\",\n", "        \"Deep Learning\",\n", "        \"Robotics\",\n", "    ],\n", "}\n", "\n", "education_levels = [\n", "    \"High School\",\n", "    \"Diploma\",\n", "    \"BSc Nursing\",\n", "    \"Associate Degree\",\n", "    \"Commercial License\",\n", "    \"M Tech\",\n", "    \"B Tech\",\n", "    \"PHD\",\n", "    \"Bachelor's Degree\",\n", "    \"Master's Degree\",\n", "    \"Certificate Program\",\n", "]\n", "\n", "languages = [\n", "    (\"German\", [\"A1\", \"A2\", \"B1\", \"B2\", \"C1\", \"C2\"]),\n", "    (\"English\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "    (\"Spanish\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "    (\"French\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "    (\"Chinese\", [\"Basic\", \"Intermediate\", \"Fluent\"]),\n", "]\n", "\n", "# Connect to SQLite\n", "conn = sqlite3.connect(\"candidates_details_500.db\")\n", "cursor = conn.cursor()\n", "\n", "# Create table\n", "cursor.execute(\n", "    \"\"\"\n", "    CREATE TABLE IF NOT EXISTS candidates (\n", "        id INTEGER PRIMARY KEY AUTOINCREMENT,\n", "        name TEXT,\n", "        age INTEGER,\n", "        country TEXT,\n", "        profession TEXT,\n", "        skills TEXT,\n", "        education TEXT,\n", "        experience_years INTEGER,\n", "        language TEXT\n", "    )\n", "    \"\"\"\n", ")\n", "\n", "# Insert 500 records\n", "for _ in range(500):\n", "    name = fake.name()\n", "    age = random.randint(22, 60)\n", "    country = fake.country()\n", "    profession = random.choice(professions)\n", "\n", "    # Randomly skip 30% of skills\n", "    if profession in skills_map:\n", "        skills = (\n", "            \", \".join(\n", "                random.sample(\n", "                    skills_map[profession],\n", "                    random.randint(1, len(skills_map[profession])),\n", "                )\n", "            )\n", "            if random.random() > 0.3\n", "            else None\n", "        )\n", "    else:\n", "        skills = None\n", "\n", "    # Randomly skip 20% of education\n", "    education = random.choice(education_levels) if random.random() > 0.2 else None\n", "\n", "    experience = random.randint(1, 20)\n", "\n", "    # Randomly skip 25% of language\n", "    if random.random() > 0.25:\n", "        lang, levels = random.choice(languages)\n", "        language = f\"{lang} {random.choice(levels)}\"\n", "    else:\n", "        language = None\n", "\n", "    # Insert into table\n", "    cursor.execute(\n", "        \"\"\"\n", "        INSERT INTO candidates (name, age, country, profession, skills, education, experience_years, language)\n", "        VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n", "        \"\"\",\n", "        (name, age, country, profession, skills, education, experience, language),\n", "    )\n", "\n", "conn.commit()\n", "conn.close()\n", "\n", "print(\"✅ Candidates inserted into SQLite with some missing values using Faker.\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce216487", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}