import json
from typing import List, Optional, Dict
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Text, create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from openai import OpenAI

# --------------------
# 1. OpenAI Client Setup
# --------------------
api_key = "********************************************************************************************************************************************************************"
client = OpenAI(api_key=api_key)

# --------------------
# 2. SQLAlchemy & DB Setup
# --------------------
Base = declarative_base()
engine = create_engine("sqlite:///candidates.db")  # persistent storage
Session = sessionmaker(bind=engine)
session = Session()


class Candidate(Base):
    __tablename__ = "candidates"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    profession = Column(String)
    experience_years = Column(Integer)
    skills = Column(Text)
    education = Column(String)
    languages = Column(Text)
    certifications = Column(Text)
    endorsements = Column(Text)
    licenses = Column(Text)


Base.metadata.create_all(engine)

# --------------------
# 3. Pydantic Schema
# --------------------
class SearchQuery(BaseModel):
    role: str
    specialization: Optional[str] = None
    experience_years: Optional[int] = None
    education: Optional[str] = None
    language: Optional[str] = None
    language_proficiency: Optional[str] = None
    certifications: Optional[List[str]] = []
    endorsements: Optional[List[str]] = []
    licenses: Optional[List[str]] = []

# --------------------
# 4. Function Call Schema for Filter Extraction
# --------------------
function_schema = {
    "name": "extract_candidate_filters",
    "description": "Extracts filters from a natural language query.",
    "parameters": {
        "type": "object",
        "properties": {
            "role": {"type": "string"},
            "specialization": {"type": "string"},
            "experience_years": {"type": "integer"},
            "education": {"type": "string"},
            "language": {"type": "string"},
            "language_proficiency": {"type": "string"},
            "certifications": {"type": "array", "items": {"type": "string"}},
            "endorsements": {"type": "array", "items": {"type": "string"}},
            "licenses": {"type": "array", "items": {"type": "string"}},
        },
        "required": ["role"]
    },
}

# --------------------
# 5. Extract Query Filters via LLM
# --------------------
def extract_query_filters(user_query: str) -> SearchQuery:
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You extract structured filters from user queries."},
            {"role": "user", "content": user_query},
        ],
        functions=[function_schema],
        function_call={"name": "extract_candidate_filters"},
    )
    args = json.loads(response.choices[0].message.function_call.arguments)
    return SearchQuery(**args)

# --------------------
# 6. Score Matching Candidates via LLM
# --------------------
def score_candidates_via_llm(filters: SearchQuery, candidates: List[Candidate]) -> List[Dict]:
    payload = {
        "query_filters": filters.dict(),
        "candidates": [
            {
                "name": c.name,
                "profession": c.profession,
                "experience_years": c.experience_years,
                "skills": json.loads(c.skills),
                "education": c.education,
                "languages": json.loads(c.languages),
                "certifications": json.loads(c.certifications),
                "endorsements": json.loads(c.endorsements),
                "licenses": json.loads(c.licenses),
            } for c in candidates
        ]
    }

    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a candidate scoring engine. Match candidates to the given query. Score them out of 100 and return structured JSON with match_score."},
            {"role": "user", "content": f"{json.dumps(payload)}"}
        ]
    )

    result_json = response.choices[0].message.content
    return json.loads(result_json)

# --------------------
# 7. Run Matching Flow
# --------------------
def run_matching(user_query: str):
    filters = extract_query_filters(user_query)
    candidates = session.query(Candidate).all()
    results = score_candidates_via_llm(filters, candidates)
    for r in results:
        print(json.dumps(r, indent=2, ensure_ascii=False))

# --------------------
# 8. Load Mock Candidates (Only Once)
# --------------------
def load_mock_data():
    if session.query(Candidate).count() == 0:
        mock_candidates = [
            Candidate(
                name="Anna Müller",
                profession="Nurse",
                experience_years=6,
                skills=json.dumps(["ICU"]),
                education="BSc Nursing",
                languages=json.dumps([{"language": "German", "proficiency": "B2"}]),
                certifications=json.dumps([]),
                endorsements=json.dumps([]),
                licenses=json.dumps([]),
            ),
            Candidate(
                name="John Doe",
                profession="Truck Driver",
                experience_years=3,
                skills=json.dumps(["OTR"]),
                education="High School",
                languages=json.dumps([{"language": "English", "proficiency": "C1"}]),
                certifications=json.dumps(["CDL Class A"]),
                endorsements=json.dumps(["Tanker"]),
                licenses=json.dumps(["Hazmat"]),
            ),
        ]
        session.add_all(mock_candidates)
        session.commit()

# --------------------
# 9. Execute
# --------------------
if __name__ == "__main__":
    load_mock_data()
    query = "Find me nurses with 5 years experience in ICU with German B2 proficiency"
    run_matching(query)
